import { getNanoId } from "../utils";
import JS<PERSON><PERSON> from "jszip";

/**
 * 统一的图片下载函数
 * 处理不同类型的图片下载（原始图片URL和data URL），确保都能正确下载到本地而不是在浏览器中打开
 */
export const downloadImage = async (imageSrc: string, fileName?: string): Promise<void> => {
	try {
		let blob: Blob;
		let fileExtension = "png";

		// 检查是否是 data URL
		if (imageSrc.startsWith("data:")) {
			// 处理 data URL（如 canvas 生成的图片）
			const response = await fetch(imageSrc);
			blob = await response.blob();

			// 从 data URL 中提取文件类型
			const mimeType = imageSrc.split(";")[0].split(":")[1];
			if (mimeType === "image/jpeg") {
				fileExtension = "jpg";
			} else if (mimeType === "image/png") {
				fileExtension = "png";
			} else if (mimeType === "image/webp") {
				fileExtension = "webp";
			}
		} else {
			// 处理普通 URL（如 OSS 图片）
			// 使用 fetch 获取图片数据，确保能够下载而不是在浏览器中打开
			const response = await fetch(imageSrc, {
				headers: {
					"Cache-Control": "no-cache",
				},
			});

			if (!response.ok) {
				throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
			}

			blob = await response.blob();

			// 从 URL 中提取文件扩展名
			try {
				const url = new URL(imageSrc);
				const pathParts = url.pathname.split(".");
				if (pathParts.length > 1) {
					const ext = pathParts[pathParts.length - 1].toLowerCase();
					if (["jpg", "jpeg", "png", "webp", "gif", "bmp"].includes(ext)) {
						fileExtension = ext === "jpeg" ? "jpg" : ext;
					}
				}
			} catch (e) {
				// 如果 URL 解析失败，使用默认扩展名
				console.warn("Failed to parse URL for extension, using default:", e);
			}
		}

		// 创建下载链接
		const url = URL.createObjectURL(blob);
		const link = document.createElement("a");

		// 设置下载属性和文件名
		link.href = url;
		link.download = fileName ? `${fileName}.${fileExtension}` : `image-${getNanoId(6)}.${fileExtension}`;

		// 添加到 DOM，触发下载，然后清理
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);

		// 清理 blob URL
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Failed to download image:", error);
		throw new Error("Failed to download image");
	}
};

/**
 * 创建简单的 ZIP 文件（使用 JSZip）
 */
const createZipFile = async (files: Array<{ name: string; blob: Blob }>): Promise<Blob> => {
	const zip = new JSZip();

	// 将所有文件添加到 ZIP 中
	files.forEach((file) => {
		zip.file(file.name, file.blob);
	});

	// 生成 ZIP 文件
	const zipBlob = await zip.generateAsync({ type: "blob" });
	return zipBlob;
};

/**
 * 批量下载多个图片
 * 如果只有一个图片，直接下载；如果多个图片，打包成 ZIP 下载
 */
export const downloadImages = async (images: Array<{ src: string; fileName?: string }>): Promise<void> => {
	if (images.length === 0) return;

	if (images.length === 1) {
		// 单个图片直接下载
		await downloadImage(images[0].src, images[0].fileName);
		return;
	}

	// 多个图片打包成 ZIP 下载
	try {
		const files: Array<{ name: string; blob: Blob }> = [];

		// 获取所有图片的 blob 数据
		for (let i = 0; i < images.length; i++) {
			const image = images[i];
			let blob: Blob;
			let fileExtension = "png";

			// 检查是否是 data URL
			if (image.src.startsWith("data:")) {
				const response = await fetch(image.src);
				blob = await response.blob();

				// 从 data URL 中提取文件类型
				const mimeType = image.src.split(";")[0].split(":")[1];
				if (mimeType === "image/jpeg") {
					fileExtension = "jpg";
				} else if (mimeType === "image/png") {
					fileExtension = "png";
				} else if (mimeType === "image/webp") {
					fileExtension = "webp";
				}
			} else {
				// 处理普通 URL
				const response = await fetch(image.src, {
					headers: {
						"Cache-Control": "no-cache",
					},
				});

				if (!response.ok) {
					console.error(`Failed to fetch image ${i + 1}: ${response.status} ${response.statusText}`);
					continue; // 跳过失败的图片
				}

				blob = await response.blob();

				// 从 URL 中提取文件扩展名
				try {
					const url = new URL(image.src);
					const pathParts = url.pathname.split(".");
					if (pathParts.length > 1) {
						const ext = pathParts[pathParts.length - 1].toLowerCase();
						if (["jpg", "jpeg", "png", "webp", "gif", "bmp"].includes(ext)) {
							fileExtension = ext === "jpeg" ? "jpg" : ext;
						}
					}
				} catch (e) {
					console.warn("Failed to parse URL for extension, using default:", e);
				}
			}

			const fileName = image.fileName ? `${image.fileName}.${fileExtension}` : `image-${i + 1}.${fileExtension}`;
			files.push({ name: fileName, blob });
		}

		if (files.length === 0) {
			throw new Error("No images could be processed");
		}

		// 创建 ZIP 文件
		const zipBlob = await createZipFile(files);

		// 下载 ZIP 文件
		const url = URL.createObjectURL(zipBlob);
		const link = document.createElement("a");
		link.href = url;
		link.download = `images-${getNanoId(6)}.zip`;

		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);

		// 清理 blob URL
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Failed to create ZIP download:", error);
		// 如果 ZIP 下载失败，回退到逐个下载
		console.log("Falling back to individual downloads...");
		const downloadPromises = images.map((image, index) => {
			return new Promise<void>((resolve) => {
				setTimeout(async () => {
					try {
						await downloadImage(image.src, image.fileName || `image-${index + 1}`);
						resolve();
					} catch (error) {
						console.error(`Failed to download image ${index + 1}:`, error);
						resolve(); // 继续下载其他图片，即使某个失败
					}
				}, index * 100); // 每个下载间隔100ms
			});
		});

		await Promise.all(downloadPromises);
	}
};
