import { sendGTMEvent } from "@next/third-parties/google";

export const EVENT_SIGN_IN = "login";

//Purchase
export const EVENT_OPEN_PLAN_ID = "openPlan";
//Purchase
export const EVENT_CHECKOUT = "begin_checkout";

export const EVENT_GENERATE_IMAGE = "generate_image";
export const EVENT_EDIT_IMAGE = "edit_image";
export const EVENT_EDIT_IMAGE_WITH_TOOL = "edit_image_with_tool";

export function trackGTMEvent(eventName: string, options?: any) {
	if (process.env.NODE_ENV === "production") {
		sendGTMEvent({
			event: eventName,
			...options,
		});
	}
}
