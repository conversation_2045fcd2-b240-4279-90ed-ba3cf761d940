import { useState, useCallback } from "react";
import type { PlacedImage } from "@/@types/canvas";
import { useImagesHistory } from "@/lib/hooks/useImagesHistory";

export type Viewport = {
	x: number;
	y: number;
	scale: number;
};

export function useCanvasState({ hasUploadingImages }: { hasUploadingImages: boolean }) {
	const { images, setImages, update, reset, undo, redo, canUndo, canRedo } = useImagesHistory([]);
	const [selectedIds, setSelectedIds] = useState<string[]>([]);
	const [viewport, setViewport] = useState<Viewport>({
		x: 0,
		y: 0,
		scale: 0.25,
	});
	const [croppingImageId, setCroppingImageId] = useState<string | null>(null);
	const [isDraggingImage, setIsDraggingImage] = useState(false);
	const [dragStartPositions, setDragStartPositions] = useState<Map<string, { x: number; y: number }>>(new Map());

	const applyWithHistory = useCallback(
		(updateFn: (prev: PlacedImage[]) => PlacedImage[], actionName: string) => {
			if (hasUploadingImages) {
				// 直接更新状态，不保存到历史记录
				setImages(updateFn);
				return;
			}
			update(updateFn, { commit: true, label: actionName });
		},
		[update],
	);

	return {
		images,
		setImages,
		update,
		reset,
		undo,
		redo,
		canUndo,
		canRedo,
		applyWithHistory,
		selectedIds,
		setSelectedIds,
		viewport,
		setViewport,
		croppingImageId,
		setCroppingImageId,
		isDraggingImage,
		setIsDraggingImage,
		dragStartPositions,
		setDragStartPositions,
	};
}
