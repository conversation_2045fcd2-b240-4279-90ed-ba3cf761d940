import { useState, useEffect, useCallback } from "react";
import { canvasStorage, type CanvasState } from "@/lib/storage";
import type { PlacedImage } from "@/@types/canvas";
import { imageToCanvasElement } from "@/lib/utils-canvas";
import { toast } from "sonner";
import type { Viewport } from "./useCanvasState";

interface UseStoragePersistenceProps {
	images: PlacedImage[];
	setImages: (images: PlacedImage[]) => void;
	viewport: Viewport;
	setViewport: (viewport: Viewport) => void;
	resetHistory: (images: PlacedImage[]) => void;
	hasUploadingImages: boolean;
}

export function useStoragePersistence({ images, setImages, viewport, setViewport, resetHistory, hasUploadingImages }: UseStoragePersistenceProps) {
	const [isStorageLoaded, setIsStorageLoaded] = useState(false);
	const [showInitialView, setShowInitialView] = useState(false);

	const saveToStorage = useCallback(async () => {
		try {
			const canvasState: CanvasState = {
				elements: [...images.map(imageToCanvasElement)],
				lastModified: Date.now(),
				viewport: viewport,
			};
			canvasStorage.saveCanvasState(canvasState);
		} catch (error) {
			console.error("Failed to save to storage:", error);
		}
	}, [images, viewport]);

	const loadFromStorage = useCallback(async () => {
		try {
			const canvasState = canvasStorage.getCanvasState();
			if (!canvasState) {
				setShowInitialView(true);
				return;
			}

			const loadedImages: PlacedImage[] = canvasState.elements
				.filter((element) => element.type === "image" && element.ossUrl)
				.map((element) => ({
					id: element.id,
					src: element.ossUrl!,
					x: element.transform.x,
					y: element.transform.y,
					width: element.width || 300,
					height: element.height || 300,
					rotation: element.transform.rotation,
					status: element.status,
					ossUrl: element.ossUrl,
					...(element.transform.cropBox && {
						cropX: element.transform.cropBox.x,
						cropY: element.transform.cropBox.y,
						cropWidth: element.transform.cropBox.width,
						cropHeight: element.transform.cropBox.height,
					}),
				}));

			if (loadedImages.length > 0) {
				setImages(loadedImages);
				resetHistory(loadedImages);
			} else {
				setShowInitialView(true);
			}

			if (canvasState.viewport) {
				setViewport(canvasState.viewport);
			}
		} catch (error) {
			console.error("Failed to load from storage:", error);
			toast.error("Failed to restore canvas", { description: "Starting with a fresh canvas" });
		} finally {
			setIsStorageLoaded(true);
		}
	}, [setImages, setViewport, resetHistory]);

	useEffect(() => {
		if (process.env.NODE_ENV === "development") {
			loadFromStorage();
		} else {
			setShowInitialView(true);
			setIsStorageLoaded(true);
		}
	}, []);

	useEffect(() => {
		if (!isStorageLoaded || hasUploadingImages) return;
		const timeoutId = setTimeout(() => {
			saveToStorage();
		}, 1000);
		return () => clearTimeout(timeoutId);
	}, [images, viewport, isStorageLoaded, hasUploadingImages, saveToStorage]);

	return { isStorageLoaded, showInitialView, setShowInitialView, saveToStorage };
}
