import type { PlacedImage } from "@/@types/canvas";

type ApplyWithHistory = (updateFn: (prev: PlacedImage[]) => PlacedImage[], actionName: string) => void;

export async function createCroppedImage(imageSrc: string, cropX: number, cropY: number, cropWidth: number, cropHeight: number): Promise<string> {
	return new Promise((resolve, reject) => {
		const img = new window.Image();
		img.crossOrigin = "anonymous";
		img.onload = () => {
			const canvas = document.createElement("canvas");
			const ctx = canvas.getContext("2d");
			if (!ctx) {
				reject(new Error("Failed to get canvas context"));
				return;
			}
			canvas.width = cropWidth * img.naturalWidth;
			canvas.height = cropHeight * img.naturalHeight;
			ctx.drawImage(
				img,
				cropX * img.naturalWidth,
				cropY * img.naturalHeight,
				cropWidth * img.naturalWidth,
				cropHeight * img.naturalHeight,
				0,
				0,
				canvas.width,
				canvas.height,
			);
			canvas.toBlob((blob) => {
				if (!blob) {
					reject(new Error("Failed to create blob"));
					return;
				}
				const reader = new FileReader();
				reader.onload = () => resolve(reader.result as string);
				reader.onerror = reject;
				reader.readAsDataURL(blob);
			}, "image/png");
		};
		img.onerror = () => reject(new Error("Failed to load image"));
		img.src = imageSrc;
	});
}

export async function handleCombineImages({
	images,
	selectedIds,
	applyWithHistory,
	setSelectedIds,
}: {
	images: PlacedImage[];
	selectedIds: string[];
	applyWithHistory: ApplyWithHistory;
	setSelectedIds: (ids: string[]) => void;
}) {
	if (selectedIds.length < 2) return;

	const selectedImages = selectedIds.map((id) => images.find((img) => img.id === id)).filter((img): img is PlacedImage => !!img);
	const sortedImages = [...selectedImages].sort((a, b) => images.findIndex((img) => img.id === a.id) - images.findIndex((img) => img.id === b.id));

	const imageElements: { img: PlacedImage; element: HTMLImageElement }[] = [];
	for (const img of sortedImages) {
		const imgElement = new window.Image();
		imgElement.crossOrigin = "anonymous";
		imgElement.src = img.src;
		await new Promise((resolve) => (imgElement.onload = resolve));
		imageElements.push({ img, element: imgElement });
	}

	let minX = Infinity,
		minY = Infinity,
		maxX = -Infinity,
		maxY = -Infinity;
	sortedImages.forEach((img) => {
		minX = Math.min(minX, img.x);
		minY = Math.min(minY, img.y);
		maxX = Math.max(maxX, img.x + img.width);
		maxY = Math.max(maxY, img.y + img.height);
	});

	const combinedWidth = maxX - minX;
	const combinedHeight = maxY - minY;
	const optimalScale = 2; // Simplified scale for consistency

	const canvas = document.createElement("canvas");
	const ctx = canvas.getContext("2d");
	if (!ctx) return;

	canvas.width = combinedWidth * optimalScale;
	canvas.height = combinedHeight * optimalScale;

	for (const { img, element: imgElement } of imageElements) {
		const relX = (img.x - minX) * optimalScale;
		const relY = (img.y - minY) * optimalScale;
		const scaledWidth = img.width * optimalScale;
		const scaledHeight = img.height * optimalScale;

		ctx.save();
		if (img.rotation) {
			ctx.translate(relX + scaledWidth / 2, relY + scaledHeight / 2);
			ctx.rotate((img.rotation * Math.PI) / 180);
			ctx.drawImage(imgElement, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
		} else if (img.cropX !== undefined && img.cropY !== undefined && img.cropWidth !== undefined && img.cropHeight !== undefined) {
			ctx.drawImage(
				imgElement,
				img.cropX * imgElement.naturalWidth,
				img.cropY * imgElement.naturalHeight,
				img.cropWidth * imgElement.naturalWidth,
				img.cropHeight * imgElement.naturalHeight,
				relX,
				relY,
				scaledWidth,
				scaledHeight,
			);
		} else {
			ctx.drawImage(imgElement, relX, relY, scaledWidth, scaledHeight);
		}
		ctx.restore();
	}

	const dataUrl = canvas.toDataURL("image/png");
	const combinedImage: PlacedImage = {
		id: `combined-${Date.now()}`,
		src: dataUrl,
		x: minX,
		y: minY,
		width: combinedWidth,
		height: combinedHeight,
		rotation: 0,
	};

	applyWithHistory((prev) => [...prev.filter((img) => !selectedIds.includes(img.id)), combinedImage], "combine");
	setSelectedIds([combinedImage.id]);
}
