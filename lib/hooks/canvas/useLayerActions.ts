import { useCallback } from "react";
import type { PlacedImage } from "@/@types/canvas";

type ApplyWithHistory = (updateFn: (prev: PlacedImage[]) => PlacedImage[], actionName: string) => void;

export function useLayerActions(selectedIds: string[], applyWithHistory: ApplyWithHistory) {
	const sendToFront = useCallback(() => {
		if (selectedIds.length === 0) return;
		applyWithHistory((prev) => {
			const selected = selectedIds.map((id) => prev.find((img) => img.id === id)).filter(Boolean) as PlacedImage[];
			const remaining = prev.filter((img) => !selectedIds.includes(img.id));
			return [...remaining, ...selected];
		}, "sendToFront");
	}, [selectedIds, applyWithHistory]);

	const sendToBack = useCallback(() => {
		if (selectedIds.length === 0) return;
		applyWithHistory((prev) => {
			const selected = selectedIds.map((id) => prev.find((img) => img.id === id)).filter(Boolean) as PlacedImage[];
			const remaining = prev.filter((img) => !selectedIds.includes(img.id));
			return [...selected, ...remaining];
		}, "sendToBack");
	}, [selectedIds, applyWithHistory]);

	const bringForward = useCallback(() => {
		if (selectedIds.length === 0) return;
		applyWithHistory((prev) => {
			const result = [...prev];
			for (let i = result.length - 2; i >= 0; i--) {
				if (selectedIds.includes(result[i].id) && !selectedIds.includes(result[i + 1].id)) {
					[result[i], result[i + 1]] = [result[i + 1], result[i]];
				}
			}
			return result;
		}, "bringForward");
	}, [selectedIds, applyWithHistory]);

	const sendBackward = useCallback(() => {
		if (selectedIds.length === 0) return;
		applyWithHistory((prev) => {
			const result = [...prev];
			for (let i = 1; i < result.length; i++) {
				if (selectedIds.includes(result[i].id) && !selectedIds.includes(result[i - 1].id)) {
					[result[i], result[i - 1]] = [result[i - 1], result[i]];
				}
			}
			return result;
		}, "sendBackward");
	}, [selectedIds, applyWithHistory]);

	return { sendToFront, sendToBack, bringForward, sendBackward };
}

// 之前的代码备份，不要删除
// const sendToFront = useCallback(() => {
// 	if (selectedIds.length === 0) return;

// 	applyWithHistory((prev) => {
// 		// Get selected images in their current order
// 		const selectedImages = selectedIds.map((id) => prev.find((img) => img.id === id)).filter(Boolean) as PlacedImage[];

// 		// Get remaining images
// 		const remainingImages = prev.filter((img) => !selectedIds.includes(img.id));

// 		// Place selected images at the end (top layer)
// 		return [...remainingImages, ...selectedImages];
// 	}, "sendToFront");
// }, [selectedIds, applyWithHistory]);

// const sendToBack = useCallback(() => {
// 	if (selectedIds.length === 0) return;

// 	applyWithHistory((prev) => {
// 		// Get selected images in their current order
// 		const selectedImages = selectedIds.map((id) => prev.find((img) => img.id === id)).filter(Boolean) as PlacedImage[];

// 		// Get remaining images
// 		const remainingImages = prev.filter((img) => !selectedIds.includes(img.id));

// 		// Place selected images at the beginning (bottom layer)
// 		return [...selectedImages, ...remainingImages];
// 	}, "sendToBack");
// }, [selectedIds, applyWithHistory]);

// const bringForward = useCallback(() => {
// 	if (selectedIds.length === 0) return;

// 	applyWithHistory((prev) => {
// 		const result = [...prev];

// 		// Process selected images from back to front to maintain relative order
// 		for (let i = result.length - 2; i >= 0; i--) {
// 			if (selectedIds.includes(result[i].id) && !selectedIds.includes(result[i + 1].id)) {
// 				// Swap with the next image if it's not also selected
// 				[result[i], result[i + 1]] = [result[i + 1], result[i]];
// 			}
// 		}

// 		return result;
// 	}, "bringForward");
// }, [selectedIds, applyWithHistory]);

// const sendBackward = useCallback(() => {
// 	if (selectedIds.length === 0) return;

// 	applyWithHistory((prev) => {
// 		const result = [...prev];

// 		// Process selected images from front to back to maintain relative order
// 		for (let i = 1; i < result.length; i++) {
// 			if (selectedIds.includes(result[i].id) && !selectedIds.includes(result[i - 1].id)) {
// 				// Swap with the previous image if it's not also selected
// 				[result[i], result[i - 1]] = [result[i - 1], result[i]];
// 			}
// 		}

// 		return result;
// 	}, "sendBackward");
// }, [selectedIds, applyWithHistory]);
