import { useEffect } from "react";
import { checkOS } from "@/lib/utils-os";

interface ShortcutActions {
	undo: () => void;
	redo: () => void;
	handleDelete: () => void;
	handleDuplicate: () => void;
	selectAll: () => void;
	zoomIn: () => void;
	zoomOut: () => void;
	zoomToFit: () => void;
	resetZoom: () => void;
	sendToFront: () => void;
	sendToBack: () => void;
	bringForward: () => void;
	sendBackward: () => void;
	exitCropMode: () => void;
	canUndo: boolean;
	canRedo: boolean;
	hasSelection: boolean;
	isCropping: boolean;
}

export function useKeyboardShortcuts(actions: ShortcutActions) {
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			const isInputElement = (e.target as HTMLElement).matches("input, textarea");
			const modifierKey = checkOS("Mac") ? e.metaKey : e.ctrlKey;

			if (modifierKey && e.key === "z" && !e.shiftKey) {
				e.preventDefault();
				actions.undo();
			} else if (modifierKey && (e.key === "y" || (e.key === "z" && e.shiftKey))) {
				e.preventDefault();
				actions.redo();
			} else if (modifierKey && e.key === "a" && !isInputElement) {
				e.preventDefault();
				actions.selectAll();
			} else if ((e.key === "Delete" || e.key === "Backspace") && !isInputElement && actions.hasSelection) {
				e.preventDefault();
				actions.handleDelete();
			} else if (modifierKey && e.key === "d" && !isInputElement && actions.hasSelection) {
				e.preventDefault();
				actions.handleDuplicate();
			} else if ((e.key === "]" || e.key === "】") && !isInputElement && actions.hasSelection) {
				e.preventDefault();
				modifierKey ? actions.bringForward() : actions.sendToFront();
			} else if ((e.key === "[" || e.key === "【") && !isInputElement && actions.hasSelection) {
				e.preventDefault();
				modifierKey ? actions.sendBackward() : actions.sendToBack();
			} else if (e.key === "Escape" && actions.isCropping) {
				e.preventDefault();
				actions.exitCropMode();
			} else if ((e.key === "+" || e.key === "=") && !isInputElement) {
				e.preventDefault();
				actions.zoomIn();
			} else if (e.key === "-" && !isInputElement) {
				e.preventDefault();
				actions.zoomOut();
			} else if (modifierKey && e.key === "0" && !isInputElement) {
				e.preventDefault();
				actions.resetZoom();
			} else if ((e.key === "!" || e.key === "！") && !isInputElement) {
				e.preventDefault();
				actions.zoomToFit();
			}
		};

		window.addEventListener("keydown", handleKeyDown);
		return () => window.removeEventListener("keydown", handleKeyDown);
	}, [actions]);
}

// 之前的代码备份，不要删除
// Handle keyboard shortcuts
// useEffect(() => {
// 	const handleKeyDown = (e: KeyboardEvent) => {
// 		// Check if target is an input element
// 		const isInputElement = e.target && (e.target as HTMLElement).matches("input, textarea");

// 		// Check OS for proper modifier key
// 		const isMac = checkOS("Mac");
// 		const modifierKey = isMac ? e.metaKey : e.ctrlKey;

// 		// Undo/Redo
// 		if (modifierKey && e.key === "z" && !e.shiftKey) {
// 			e.preventDefault();
// 			undo();
// 		} else if (modifierKey && ((e.key === "z" && e.shiftKey) || e.key === "y")) {
// 			e.preventDefault();
// 			redo();
// 		}
// 		// Select all
// 		else if (modifierKey && e.key === "a" && !isInputElement) {
// 			e.preventDefault();
// 			setSelectedIds(images.map((img) => img.id));
// 		}
// 		// Delete
// 		else if ((e.key === "Delete" || e.key === "Backspace") && !isInputElement) {
// 			if (selectedIds.length > 0) {
// 				e.preventDefault();
// 				handleDelete();
// 			}
// 		}
// 		// Duplicate
// 		else if (modifierKey && e.key === "d" && !isInputElement) {
// 			e.preventDefault();
// 			if (selectedIds.length > 0) {
// 				handleDuplicate();
// 			}
// 		}
// 		// Group (Combine Images)  Group暂时注释掉，不开放，以后再开放
// 		// else if (modifierKey && e.key === "g" && !isInputElement) {
// 		// 	e.preventDefault();
// 		// 	if (selectedIds.length > 1) {
// 		// 		handleCombineImages();
// 		// 	}
// 		// }
// 		// Layer ordering shortcuts
// 		else if (e.key === "]" && !isInputElement) {
// 			e.preventDefault();
// 			if (selectedIds.length > 0) {
// 				if (modifierKey) {
// 					bringForward();
// 				} else {
// 					sendToFront();
// 				}
// 			}
// 		} else if (e.key === "[" && !isInputElement) {
// 			e.preventDefault();
// 			if (selectedIds.length > 0) {
// 				if (modifierKey) {
// 					sendBackward();
// 				} else {
// 					sendToBack();
// 				}
// 			}
// 		}
// 		// Escape to exit crop mode
// 		else if (e.key === "Escape" && croppingImageId) {
// 			e.preventDefault();
// 			setCroppingImageId(null);
// 		}
// 		// Zoom in
// 		else if ((e.key === "+" || e.key === "=") && !isInputElement) {
// 			e.preventDefault();
// 			const newScale = Math.min(5, viewport.scale * 1.2);
// 			const centerX = canvasSize.width / 2;
// 			const centerY = canvasSize.height / 2;

// 			const mousePointTo = {
// 				x: (centerX - viewport.x) / viewport.scale,
// 				y: (centerY - viewport.y) / viewport.scale,
// 			};

// 			setViewport({
// 				x: centerX - mousePointTo.x * newScale,
// 				y: centerY - mousePointTo.y * newScale,
// 				scale: newScale,
// 			});
// 		}
// 		// Zoom out
// 		else if (e.key === "-" && !isInputElement) {
// 			e.preventDefault();
// 			const newScale = Math.max(0.1, viewport.scale / 1.2);
// 			const centerX = canvasSize.width / 2;
// 			const centerY = canvasSize.height / 2;

// 			const mousePointTo = {
// 				x: (centerX - viewport.x) / viewport.scale,
// 				y: (centerY - viewport.y) / viewport.scale,
// 			};

// 			setViewport({
// 				x: centerX - mousePointTo.x * newScale,
// 				y: centerY - mousePointTo.y * newScale,
// 				scale: newScale,
// 			});
// 		}
// 		// Reset zoom
// 		else if (e.key === "0" && modifierKey) {
// 			e.preventDefault();
// 			setViewport({ x: 0, y: 0, scale: 1 });
// 		}
// 		// Zoom to fit
// 		else if ((e.key === "!" || e.key === "！") && !isInputElement) {
// 			e.preventDefault();
// 			if (zoomFunctions) {
// 				zoomFunctions.handleZoomToFit();
// 			}
// 		}
// 	};

// 	const handleKeyUp = (_e: KeyboardEvent) => {
// 		// Currently no key up handlers needed
// 	};

// 	window.addEventListener("keydown", handleKeyDown);
// 	window.addEventListener("keyup", handleKeyUp);
// 	return () => {
// 		window.removeEventListener("keydown", handleKeyDown);
// 		window.removeEventListener("keyup", handleKeyUp);
// 	};
// }, [
// 	selectedIds,
// 	images,
// 	undo,
// 	redo,
// 	handleDelete,
// 	handleDuplicate,
// 	handleRun,
// 	croppingImageId,
// 	viewport,
// 	canvasSize,
// 	sendToFront,
// 	sendToBack,
// 	bringForward,
// 	sendBackward,
// ]);
