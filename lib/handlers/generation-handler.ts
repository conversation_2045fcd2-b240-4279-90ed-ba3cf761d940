import type { PlacedImage, GenerationSettings } from "@/@types/canvas";
import { toast } from "sonner";
import { EVENT_EDIT_IMAGE, trackGTMEvent } from "../track-events";
import { UserInfoDB } from "@/@types/user";

interface GenerationHandlerDeps {
	images: PlacedImage[];
	selectedIds: string[];
	generationSettings: GenerationSettings;
	canvasSize: { width: number; height: number };
	viewport: { x: number; y: number; scale: number };
	setImages: React.Dispatch<React.SetStateAction<PlacedImage[]>>;
	setSelectedIds: React.Dispatch<React.SetStateAction<string[]>>;
	setIsGenerating: React.Dispatch<React.SetStateAction<boolean>>;
	generateTextToImage: (params: any) => Promise<any>;
	editImage: (params: { prompt: string; images: string[] }) => Promise<{ resultUrl: string }>;
	applyWithHistory: (updateFn: (prev: PlacedImage[]) => PlacedImage[], actionName: string) => void;
	membershipLevel?: string;
	refreshUser: () => void;
}

const createPlaceholderForEdit = ({
	setImages,
	selectedImages,
}: {
	setImages: GenerationHandlerDeps["setImages"];
	selectedImages: PlacedImage[];
}): { placeholderId: string; centerY: number } => {
	const placeholderId = `generated-${Date.now()}-${Math.random()}`;
	const placeholderSize = 1024;

	// Calculate position for the new generated image
	// Place it to the right of the rightmost selected image
	let maxX = -Infinity;
	let centerY = 0;
	selectedImages.forEach((img) => {
		maxX = Math.max(maxX, img.x + img.width);
		centerY += img.y + img.height / 2;
	});
	centerY /= selectedImages.length;

	// Create placeholder first
	setImages((prev) => [
		...prev,
		{
			id: placeholderId,
			src: "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
			x: maxX + 20,
			y: centerY - placeholderSize / 2,
			width: placeholderSize,
			height: placeholderSize,
			rotation: 0,
			status: "generating",
		},
	]);

	return { placeholderId, centerY };
};

export const handleRun = async (deps: GenerationHandlerDeps) => {
	const {
		images,
		selectedIds,
		generationSettings,
		canvasSize,
		viewport,
		setImages,
		setSelectedIds,
		setIsGenerating,
		generateTextToImage,
		editImage,
		applyWithHistory,
		membershipLevel,
		refreshUser,
	} = deps;

	if (!generationSettings.prompt) {
		toast.error("No Prompt", {
			description: "Please enter a prompt to generate an image",
		});
		return;
	}

	const selectedImages = images.filter((img) => selectedIds.includes(img.id));

	// If no images are selected, do text-to-image generation
	if (selectedImages.length === 0) {
		if (process.env.NODE_ENV === "production") {
			toast.warning("Not available", {
				description: "Text-to-image generation is not supported yet.",
			});
			return;
		}

		setIsGenerating(true);
		// Create placeholder first
		const placeholderId = `generated-${Date.now()}-${Math.random()}`;
		const placeholderSize = 1024; // 正方形占位符尺寸
		// Place at center of viewport
		const viewportCenterX = (canvasSize.width / 2 - viewport.x) / viewport.scale;
		const viewportCenterY = (canvasSize.height / 2 - viewport.y) / viewport.scale;

		setImages((prev) => [
			...prev,
			{
				id: placeholderId,
				src: "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
				x: viewportCenterX - placeholderSize / 2,
				y: viewportCenterY - placeholderSize / 2,
				width: placeholderSize,
				height: placeholderSize,
				rotation: 0,
				isGenerated: true,
				status: "generating",
			},
		]);

		try {
			trackGTMEvent(EVENT_EDIT_IMAGE, {
				membership_level: membershipLevel,
				tool: "edit-image",
			});
			const result = await generateTextToImage({
				prompt: generationSettings.prompt,
				imageSize: "square",
			});

			if (result.resultUrl) {
				// Load the generated image to get actual dimensions
				const img = new window.Image();
				img.crossOrigin = "anonymous";

				img.onload = () => {
					// Update the placeholder with actual image and real dimensions
					applyWithHistory(
						(prev) =>
							prev.map((imgItem) =>
								imgItem.id === placeholderId
									? {
											...imgItem,
											src: result.url,
											width: img.width,
											height: img.height,
											status: "completed",
											ossUrl: result.url,
										}
									: imgItem,
							),
						"generate-image",
					);
					refreshUser();
				};

				img.onerror = () => {
					// If image loading fails, use dimensions from API result
					setImages((prev) =>
						prev.map((imgItem) =>
							imgItem.id === placeholderId
								? {
										...imgItem,
										src: result.url,
										width: result.width,
										height: result.height,
										status: "completed",
										ossUrl: result.url,
									}
								: imgItem,
						),
					);
				};

				img.src = result.url;

				// Select the new image
				setSelectedIds([placeholderId]);
				toast.success("Image generated successfully!");
			} else {
				// remove placeholder
				setImages((prev) => prev.filter((img) => img.id !== placeholderId));
				throw new Error("No result image received");
			}
		} catch (error: any) {
			// remove placeholder
			setImages((prev) => prev.filter((img) => img.id !== placeholderId));
			throw error;
		} finally {
			setIsGenerating(false);
		}
		return;
	}

	// Check if more than 10 images are selected
	if (selectedImages.length > 10) {
		toast.warning("Too many images selected", {
			description: "Maximum 10 images allowed for editing",
		});
		return;
	}

	// Collect image URLs from selected images
	const imageUrls: string[] = [];

	for (const img of selectedImages) {
		// Use ossUrl if available, otherwise use src
		const imageUrl = img.ossUrl || img.src;
		imageUrls.push(imageUrl);
	}

	setIsGenerating(true);

	// create a placeholder
	const { placeholderId, centerY } = createPlaceholderForEdit({ setImages, selectedImages });
	try {
		trackGTMEvent(EVENT_EDIT_IMAGE, {
			membership_level: membershipLevel,
			tool: "edit",
		});
		// Call orpc.image.editImage with multiple images
		const result = await editImage({
			prompt: generationSettings.prompt,
			images: imageUrls,
		});

		if (result.resultUrl) {
			// Load the generated image to get actual dimensions
			const img = new window.Image();
			img.crossOrigin = "anonymous";

			img.onload = () => {
				// Update with actual image and dimensions
				applyWithHistory(
					(prev) =>
						prev.map((imgItem) =>
							imgItem.id === placeholderId
								? {
										...imgItem,
										src: result.resultUrl,
										width: img.width,
										height: img.height,
										y: centerY - img.height / 2,
										status: "completed",
										ossUrl: result.resultUrl,
									}
								: imgItem,
						),
					"edit-image",
				);
				refreshUser();
			};

			img.onerror = () => {
				// If image loading fails, use default dimensions
				setImages((prev) =>
					prev.map((imgItem) =>
						imgItem.id === placeholderId
							? {
									...imgItem,
									src: result.resultUrl,
									status: "completed",
									ossUrl: result.resultUrl,
								}
							: imgItem,
					),
				);
			};

			img.src = result.resultUrl;

			// Select the new generated image
			setSelectedIds([placeholderId]);

			toast.success("Image generated successfully!");
		} else {
			throw new Error("No result image received");
		}
	} catch (error: any) {
		// remove placeholder
		setImages((prev) => prev.filter((img) => img.id !== placeholderId));
		throw error;
	} finally {
		setIsGenerating(false);
	}
};
