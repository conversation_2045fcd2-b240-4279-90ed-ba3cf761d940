import { getDB } from "./db/db-client.server";
import { eq, desc } from "drizzle-orm";
import superjson from "superjson";
import { MediaTask, mediaTaskSchema } from "./db/schema.server";
import { getKVKeyMediaTask } from "@/lib/utils";
import { getValue, setValue } from "./kv/redis-upstash.server";
import { DURATION_2_HOUR } from "@/lib/constants";

export async function getMediTask(requestId: string, userId: string | null): Promise<MediaTask | null> {
	let mediaTask: MediaTask | null = null;

	//先从kv中获取
	const cacheKey = getKVKeyMediaTask(requestId);
	const kvDataMediaTask = (await getValue(cacheKey)) as any;
	if (kvDataMediaTask) {
		try {
			mediaTask = superjson.deserialize(kvDataMediaTask) as MediaTask;
		} catch (error) {
			console.error("[getMediaTask] parse media task data from redis error:", error);
		}
	}

	//再从db中获取
	if (!mediaTask) {
		const db = getDB();
		const [newMediaTask]: MediaTask[] = await db.select().from(mediaTaskSchema).where(eq(mediaTaskSchema.thirdRequestId, requestId));
		if (newMediaTask) {
			await setValue(cacheKey, superjson.stringify(newMediaTask), DURATION_2_HOUR);
			mediaTask = newMediaTask;
		}
	}

	if (!mediaTask) {
		return null;
	}

	if (userId && mediaTask.userId !== userId) {
		return null;
	}

	return mediaTask;
}
