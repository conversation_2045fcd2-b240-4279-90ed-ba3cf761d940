import { falGenImage } from "./fal-config.server";

export async function removeBackground(image: string): Promise<string> {
	let falAIEndPoint = "fal-ai/imageutils/rembg";
	let payload: any = {
		image_url: image,
	};
	console.log("fal.ai rembg payload: ", payload);
	console.log("fal.ai rembg endpoint: ", falAIEndPoint);

	const resultUrl: string = await falGenImage(falAIEndPoint, payload);

	return resultUrl;
}
