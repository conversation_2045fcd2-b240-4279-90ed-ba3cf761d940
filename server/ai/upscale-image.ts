import { falGenImage } from "./fal-config.server";

export async function upscaleImage(image: string): Promise<string> {
	let falAIEndPoint = "fal-ai/recraft/upscale/crisp";
	let payload: any = {
		image_url: image,
	};
	console.log("fal.ai upscale payload: ", payload);
	console.log("fal.ai upscale endpoint: ", falAIEndPoint);

	const resultUrl: string = await falGenImage(falAIEndPoint, payload);

	return resultUrl;
}
