import { z } from "zod";
import { protectedProcedure } from "../init";
import { getUUIDString } from "@/lib/utils";
import { saveToR2 } from "@/server/r2.server";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { genGemini2_5FromFal } from "@/server/ai/gemini.server";
import { getDB } from "@/server/db/db-client.server";
import { mediaTaskSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_EDIT_IMAGE, EVENT_GENERATE_IMAGE } from "@/lib/track-events";
import { constructErrorFalAI, handleApiErrorEvent } from "@/@types/error-api";
import { handleUnifiedError } from "@/@types/error";

export const imageRouter = {
	editImage: protectedProcedure
		.input(
			z.object({
				prompt: z.string().min(1, "Prompt is required"),
				images: z.array(z.string().url("Invalid image URL")).min(1, "At least one image is required"),
			}),
		)
		.handler(async ({ input, context }) => {
			console.log("editImage params: ", input);
			try {
				// return {
				// 	resultUrl: "https://static.editpal.im/mkt/home/<USER>",
				// };
				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 5;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
				});

				// 调用 AI 服务生成图片
				const resultUrls: string[] = await genGemini2_5FromFal(input.prompt, 1, input.images);
				const resultUrl = resultUrls[0];

				// 保存到 R2
				const imagePath = await saveToR2(resultUrl);

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					await tx.insert(mediaTaskSchema).values({
						userId: userId,
						status: MediaResultStatus.Completed,
						type: MediaHeadType.Image,
						tool: MediaHeadToolType.ImageEditor,
						visibility: false,
						requestBody: JSON.stringify(input),
						creditsSources: JSON.stringify(creditConsumes),
						ip: context.cfIp,
					});
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image generation result head uid: ${imageResultId}.`,
				});

				// 返回结果
				return {
					resultUrl: `${OSS_URL_HOST}/${imagePath}`,
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.editImage`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),

	generateTextToImage: protectedProcedure
		.input(
			z.object({
				prompt: z.string(),
				seed: z.number().optional(),
				imageSize: z.enum(["landscape_4_3", "portrait_4_3", "square", "landscape_16_9", "portrait_16_9"]).optional(),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				// return {
				// 	url: "https://static.editpal.im/mkt/home/<USER>",
				// 	width: 1024,
				// 	height: 1024,
				// 	seed: -1,
				// };

				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 5;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_GENERATE_IMAGE, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
				});

				// 调用 AI 服务生成图片
				const resultUrls: string[] = await genGemini2_5FromFal(input.prompt, 1);
				const resultUrl = resultUrls[0];

				// 保存到 R2
				const imagePath = await saveToR2(resultUrl);

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					await tx.insert(mediaTaskSchema).values({
						userId: userId,
						status: MediaResultStatus.Completed,
						type: MediaHeadType.Image,
						tool: MediaHeadToolType.TextToImage,
						visibility: false,
						requestBody: JSON.stringify(input),
						creditsSources: JSON.stringify(creditConsumes),
						ip: context.cfIp,
					});
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image generation result head uid: ${imageResultId}.`,
				});

				// 返回结果
				return {
					resultUrl: `${OSS_URL_HOST}/${imagePath}`,
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.generateTextToImage`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),
};
