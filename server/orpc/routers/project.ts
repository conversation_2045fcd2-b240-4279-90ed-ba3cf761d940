import { z } from "zod";
import { protectedProcedure } from "../init";
import { WEBNAME } from "@/lib/constants";
import { handleApiErrorEvent } from "@/@types/error-api";
import { handleUnifiedError } from "@/@types/error";
import { mediaProjectSchema, MediaProject, NewMediaProject } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { and, eq, desc, gt } from "drizzle-orm";
import { subDays } from "date-fns";
import { getUUIDString } from "@/lib/utils";
import { ProjectStatusType } from "@/@types/media/project-type";

export const projectRouter = {
	list: protectedProcedure.handler(async ({ context }) => {
		try {
			const userId = context.sessionUser?.id!;

			// 获取最新30天的项目
			const db = getDB();
			const projects: MediaProject[] = await db
				.select()
				.from(mediaProjectSchema)
				.where(
					and(
						eq(mediaProjectSchema.userId, userId),
						eq(mediaProjectSchema.status, ProjectStatusType.Active),
						gt(mediaProjectSchema.createdAt, subDays(new Date(), 30)),
					),
				)
				.orderBy(desc(mediaProjectSchema.id));

			return {
				projects: projects.map((project) => ({
					id: project.uid,
					name: project.name,
					coverList: project.coverList ? JSON.parse(project.coverList) : [],
				})),
			};
		} catch (error) {
			handleApiErrorEvent(error, `${WEBNAME} - rpc:  project.list`);
			throw handleUnifiedError(error, "rpc");
		}
	}),
	create: protectedProcedure.handler(async ({ context, input }) => {
		try {
			const userId = context.sessionUser?.id!;
			const projectId = getUUIDString();

			const db = getDB();
			const newProject: NewMediaProject = {
				userId,
				uid: projectId,
				viewport: JSON.stringify({ x: 0, y: 0, scale: 0.25 }),
				elements: JSON.stringify([]),
				coverList: JSON.stringify([]),
			};
			await db.insert(mediaProjectSchema).values(newProject);
			return { project_id: projectId };
		} catch (error) {
			handleApiErrorEvent(error, `${WEBNAME} - rpc:  project.create`);
			throw handleUnifiedError(error, "rpc");
		}
	}),
	get: protectedProcedure
		.input(
			z.object({
				id: z.string().min(1, "Project ID is required"),
			}),
		)
		.handler(async ({ context, input }) => {
			try {
				const userId = context.sessionUser?.id!;
				const projectUid = input.id;

				const db = getDB();
				const [project]: MediaProject[] = await db
					.select()
					.from(mediaProjectSchema)
					.where(
						and(
							eq(mediaProjectSchema.uid, projectUid),
							eq(mediaProjectSchema.status, ProjectStatusType.Active),
							eq(mediaProjectSchema.userId, userId),
						),
					);

				if (!project) {
					return { project: null };
				}

				return {
					project: {
						id: project.uid,
						name: project.name,
						coverList: project.coverList ? JSON.parse(project.coverList) : [],
						viewport: project.viewport ? JSON.parse(project.viewport) : { x: 0, y: 0, scale: 0.25 },
						elements: project.elements ? JSON.parse(project.elements) : [],
					},
				};
			} catch (error) {
				handleApiErrorEvent(error, `${WEBNAME} - rpc:  project.get`);
				throw handleUnifiedError(error, "rpc");
			}
		}),
	save: protectedProcedure
		.input(
			z.object({
				id: z.string().min(1, "Project ID is required"),
				coverList: z.array(z.string().url("Invalid cover URL")),
				viewport: z.object({
					x: z.number(),
					y: z.number(),
					scale: z.number(),
				}),
				elements: z.string().min(1, "Elements is required"),
			}),
		)
		.handler(async ({ context, input }) => {
			try {
				const userId = context.sessionUser?.id!;
				const projectUid = input.id;
				const viewport = input.viewport;
				const elements = input.elements;

				const db = getDB();
				await db
					.update(mediaProjectSchema)
					.set({
						viewport: JSON.stringify(viewport),
						elements: elements,
						coverList: JSON.stringify(input.coverList.slice(0, 4)), // 限制coverList最多4个元素
					})
					.where(and(eq(mediaProjectSchema.uid, projectUid), eq(mediaProjectSchema.userId, userId)));

				return { status: "success" };
			} catch (error) {
				handleApiErrorEvent(error, `${WEBNAME} - rpc:  project.save`);
				throw handleUnifiedError(error, "rpc");
			}
		}),
	delete: protectedProcedure
		.input(
			z.object({
				id: z.string().min(1, "Project ID is required"),
			}),
		)
		.handler(async ({ context, input }) => {
			try {
				const userId = context.sessionUser?.id!;
				const projectUid = input.id;

				const db = getDB();
				await db
					.update(mediaProjectSchema)
					.set({
						status: ProjectStatusType.Deleted,
					})
					.where(and(eq(mediaProjectSchema.uid, projectUid), eq(mediaProjectSchema.userId, userId)));

				return { status: "success" };
			} catch (error) {
				handleApiErrorEvent(error, `${WEBNAME} - rpc:  project.delete`);
				throw handleUnifiedError(error, "rpc");
			}
		}),
	changeProjectName: protectedProcedure
		.input(
			z.object({
				id: z.string().min(1, "Project ID is required"),
				name: z.string().min(1, "Name is required"),
			}),
		)
		.handler(async ({ context, input }) => {
			try {
				const userId = context.sessionUser?.id!;
				const projectUid = input.id;

				const db = getDB();
				await db
					.update(mediaProjectSchema)
					.set({
						name: input.name,
					})
					.where(and(eq(mediaProjectSchema.uid, projectUid), eq(mediaProjectSchema.userId, userId)));

				return { status: "success", newName: input.name };
			} catch (error) {
				handleApiErrorEvent(error, `${WEBNAME} - rpc:  project.changeProjectName`);
				throw handleUnifiedError(error, "rpc");
			}
		}),
};
