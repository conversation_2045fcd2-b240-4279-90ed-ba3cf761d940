import { z } from "zod";
import { protectedProcedure } from "../init";
import { getUUIDString } from "@/lib/utils";
import { saveToR2 } from "@/server/r2.server";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { getDB } from "@/server/db/db-client.server";
import { mediaTaskSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_EDIT_IMAGE_WITH_TOOL } from "@/lib/track-events";
import { constructErrorFalAI, handleApiErrorEvent } from "@/@types/error-api";
import { handleUnifiedError } from "@/@types/error";
import { removeBackground } from "@/server/ai/remove-background";
import { upscaleImage } from "@/server/ai/upscale-image";

export const imageToolRouter = {
	removeBackground: protectedProcedure
		.input(
			z.object({
				imageUrl: z.string().url(),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 1;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});

				console.log("removeBackground params: ", input);
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE_WITH_TOOL, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
					tool: "remove-background",
				});

				// 调用 AI 服务生成图片
				const resultUrl: string = await removeBackground(input.imageUrl);

				// 保存到 R2
				const imagePath = await saveToR2(resultUrl);

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					await tx.insert(mediaTaskSchema).values({
						userId: userId,
						status: MediaResultStatus.Completed,
						type: MediaHeadType.Image,
						tool: MediaHeadToolType.ImageTool,
						visibility: false,
						requestBody: JSON.stringify(input),
						creditsSources: JSON.stringify(creditConsumes),
						ip: context.cfIp,
					});
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image remove background result head uid: ${imageResultId}.`,
				});

				// 返回结果
				return {
					resultUrl: `${OSS_URL_HOST}/${imagePath}`,
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.removeBackground`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),
	upscale: protectedProcedure
		.input(
			z.object({
				imageUrl: z.string().url(),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 1;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});

				console.log("upscale params: ", input);
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE_WITH_TOOL, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
					tool: "upscale-image",
				});

				// 调用 AI 服务生成图片
				const resultUrl: string = await upscaleImage(input.imageUrl);

				// 保存到 R2
				const imagePath = await saveToR2(resultUrl);

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					await tx.insert(mediaTaskSchema).values({
						userId: userId,
						status: MediaResultStatus.Completed,
						type: MediaHeadType.Image,
						tool: MediaHeadToolType.ImageTool,
						visibility: false,
						requestBody: JSON.stringify(input),
						creditsSources: JSON.stringify(creditConsumes),
						ip: context.cfIp,
					});
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image upscale result head uid: ${imageResultId}.`,
				});

				// 返回结果
				return {
					resultUrl: `${OSS_URL_HOST}/${imagePath}`,
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.upscale`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),
};
