import { o } from "../init";
import { UserInfoDB } from "@/@types/user";
import { refreshUser } from "@/server/refresh-user";
import { WEBNAME } from "@/lib/constants";
import { handleApiErrorEvent } from "@/@types/error-api";
import { handleUnifiedError } from "@/@types/error";

export const userRouter = {
	// 查询：根据 ID 获取用户
	getUser: o.handler(async ({ context }) => {
		try {
			const userId = context.sessionUser?.id;
			if (!userId) {
				return { userInfo: null };
			}

			const userInfo: UserInfoDB | null = await refreshUser(userId);

			return { userInfo };
		} catch (error) {
			handleApiErrorEvent(error, `${WEBNAME} - rpc:  user.getUser`);
			throw handleUnifiedError(error, "rpc");
		}
	}),
};
