import { sqliteTable, integer, text, index } from "drizzle-orm/sqlite-core";

export const mediaProjectSchema = sqliteTable(
	"media_project",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		uid: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),

		status: text("status").default("active").notNull(), // active, archived, deleted
		public: integer("public", { mode: "boolean" }).default(false).notNull(),

		name: text("name").default("Untitled").notNull(),
		coverList: text("cover_list").default("[]").notNull(), // JSON string for cover list: string[Image URL], max 4 images
		viewport: text("viewport").notNull(), // JSON string for viewport: {x: number, y: number, scale: number}
		elements: text("elements").default("[]").notNull(), // JSON string for elements: CanvasElement[]

		remark: text("remark"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.notNull(),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date())
			.notNull(),
	},
	(table) => [
		index("idx_media_project_user_uid").on(table.userId),
		index("idx_media_project_status").on(table.status),
		index("idx_media_project_created_at").on(table.createdAt),
	],
);
export type MediaProject = typeof mediaProjectSchema.$inferSelect;
export type NewMediaProject = typeof mediaProjectSchema.$inferInsert;

// ==============media generate task ===============
export const mediaTaskSchema = sqliteTable(
	"media_task",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),
		status: text("status").notNull(),

		type: text("type").default("image").notNull(), // image, video
		tool: text("tool"),
		mediaHeadUid: text("media_head_uid"),
		thirdRequestId: text("third_request_id"), // API request id

		visibility: integer("visibility", { mode: "boolean" }).default(true).notNull(),
		prompt: text("prompt"),
		videoDuration: integer("video_duration", { mode: "number" }),
		aspectRatio: text("aspect_ratio"),
		seed: text("seed"),
		requestBody: text("request_body"),

		creditsSources: text("credits_source"),
		ip: text("ip"),
		remark: text("remark"),
		error: text("error"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.notNull(),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date())
			.notNull(),
	},
	(table) => [index("idx_media_task_user_uid").on(table.userId), index("idx_media_task_third_request_id").on(table.thirdRequestId)],
);
export type MediaTask = typeof mediaTaskSchema.$inferSelect;
export type NewMediaTask = typeof mediaTaskSchema.$inferInsert;
