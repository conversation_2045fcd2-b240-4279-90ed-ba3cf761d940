"use client";

import React from "react";
import { useSession } from "@/lib/auth-client";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { MembershipID } from "@/@types/membership-type";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button, buttonVariants } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ZapIcon, CreditCard, MailIcon, LogOutIcon, FolderIcon } from "lucide-react";
import { WEBNAME, EMAIL_CONTACT } from "@/lib/constants";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useUser } from "@/hooks/use-userinfo";

export function UserMenu() {
	const { data: session } = useSession();
	const { data: userData } = useUser();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();

	if (!session) return null;

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild className="cursor-pointer">
				<div className="flex h-8 flex-row items-center gap-1 rounded-full bg-white px-1.5">
					<div className="flex h-6 flex-row items-center gap-1 px-0.5 text-xs text-green-500">
						<ZapIcon className="size-3 fill-current" /> <span>{userData?.creditsAll}</span>
					</div>
					<div className="bg-input flex shrink-0 flex-row items-center justify-center gap-2 rounded-full">
						<Avatar className="size-6">
							<AvatarImage src={session.user.image!} alt="User Avatar" />
							<AvatarFallback>{session.user.name}</AvatarFallback>
						</Avatar>
					</div>
				</div>
			</DropdownMenuTrigger>
			<DropdownMenuContent className="w-[240px] rounded-2xl border-none p-0" align="end" forceMount>
				<div className="flex gap-5 p-4">
					<Avatar className="flex size-9 shrink-0 items-center gap-2">
						<AvatarImage src={session.user.image!} alt="User Avatar" />
						<AvatarFallback>{session.user.name}</AvatarFallback>
					</Avatar>
					<div className="flex min-w-0 flex-1 flex-col items-start">
						<p className="truncate text-sm font-semibold">{session.user.name ?? WEBNAME}</p>
						<p className="text-muted-foreground mt-1 truncate text-xs">{session.user.email ?? "--"}</p>
					</div>
				</div>
				{userData?.user?.membershipId === MembershipID.Free && (
					<div className="px-[16px] pb-4">
						<Button size="sm" className="w-full bg-blue-500 hover:bg-blue-500/80" onClick={() => setPlanBoxOpen(true)}>
							Get a plan
						</Button>
					</div>
				)}
				<Separator className="bg-muted" />
				<div className="space-y-1 px-1 py-2">
					<Link href="/assets" className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}>
						<div className="flex flex-row items-center">
							<FolderIcon className="mr-3 h-4 w-4 shrink-0" />
							My Assets
						</div>
					</Link>
					<Link href="/user/my-billing" className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}>
						<div className="flex flex-row items-center">
							<CreditCard className="mr-3 h-4 w-4 shrink-0" />
							My Billing
						</div>
						<p className="flex items-center gap-1 rounded bg-zinc-300 px-2 py-0.5 text-[10px] font-medium">{userData?.user?.membershipFormatted}</p>
					</Link>
					<a href={`mailto:${EMAIL_CONTACT}`} className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}>
						<div className="flex flex-row items-center">
							<MailIcon className="mr-3 h-4 w-4 shrink-0" />
							Contact Us
						</div>
					</a>
				</div>
				<Separator className="bg-muted" />
				<div className="space-y-1 px-1 py-2">
					<button className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")} onClick={handleSignOut}>
						<div className="flex flex-row items-center">
							<LogOutIcon className="mr-3 h-4 w-4 shrink-0" />
							Sign out
						</div>
					</button>
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
