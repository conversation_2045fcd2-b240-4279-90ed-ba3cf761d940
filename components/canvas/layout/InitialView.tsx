import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LoaderCircleIcon, PlusIcon } from "lucide-react";

interface InitialViewProps {
	onUpload: () => void;
	isUploading: boolean;
}

export function InitialView({ onUpload, isUploading }: InitialViewProps) {
	return (
		<div className="pointer-events-none absolute inset-0 flex items-center justify-center">
			<div className="pointer-events-auto flex w-full max-w-xl flex-col items-center justify-center gap-8 rounded-2xl bg-white px-6 pt-14 pb-6 text-center shadow-lg sm:pt-18 sm:pb-12 md:rounded-3xl">
				<div>
					<p className="text-xl font-medium md:text-2xl">Your AI Image Editing Pal</p>
					<p className="text-muted-foreground pt-4 text-sm font-medium">Upload images to get started</p>
				</div>
				<Button
					className="relative mx-auto h-auto overflow-hidden rounded-2xl bg-blue-500 px-6 py-5 hover:bg-blue-500/80"
					onClick={onUpload}
					disabled={isUploading}
				>
					<div className="flex flex-row items-center gap-2">
						{isUploading ? (
							<LoaderCircleIcon className="h-4 w-4 animate-spin text-white" />
						) : (
							<p className="flex h-4 w-4 items-center justify-center rounded-full bg-white">
								<PlusIcon className="size-3 text-blue-500" strokeWidth={3} />
							</p>
						)}
						{isUploading ? "Uploading..." : "Upload Images"}
					</div>
				</Button>
			</div>
		</div>
	);
}
