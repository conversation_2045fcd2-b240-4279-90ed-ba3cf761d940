"use client";

import React, { useState } from "react";
import type { PlacedImage, GenerationSettings } from "@/@types/canvas";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { LoaderCircleIcon, PlusIcon, ZapIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface PromptBarProps {
	selectedIds: string[];
	images: PlacedImage[];
	onRun: (settings: GenerationSettings) => void;
	onUpload: () => void;
	isGenerating: boolean;
}

export function PromptBar({ selectedIds, images, onRun, onUpload, isGenerating }: PromptBarProps) {
	const [generationSettings, setGenerationSettings] = useState<GenerationSettings>({ prompt: "" });

	const handleRunClick = () => {
		if (generationSettings.prompt.trim() && selectedIds.length > 0) {
			onRun(generationSettings);
		}
	};

	return (
		<div className="fixed right-0 bottom-0 left-0 z-20 p-2 pb-[calc(0.5rem+env(safe-area-inset-bottom))] md:absolute md:bottom-2 md:left-1/2 md:max-w-[648px] md:-translate-x-1/2 md:transform md:p-0 md:pb-0">
			<div className={cn("bg-card/95 rounded-2xl backdrop-blur-lg", "shadow")}>
				<div className="relative flex flex-col py-2">
					{selectedIds.length > 0 && (
						<div className="flex gap-2 overflow-x-auto px-2 pb-1">
							{selectedIds.map((id) => {
								const image = images.find((img) => img.id === id);
								if (!image) return null;
								return (
									<div key={id} className="border-border/20 bg-background h-16 w-16 flex-shrink-0 overflow-hidden rounded-lg border">
										<img src={image.src} alt="" className="h-full w-full object-cover" />
									</div>
								);
							})}
						</div>
					)}

					<div className="relative">
						<Textarea
							value={generationSettings.prompt}
							onChange={(e) => setGenerationSettings({ ...generationSettings, prompt: e.target.value })}
							maxLength={1000}
							placeholder="Select images and describe your changes..."
							className="h-20 w-full resize-none border-none text-sm shadow-none placeholder:text-neutral-400 focus-visible:ring-0 [&::-webkit-scrollbar]:hidden"
							onKeyDown={(e) => {
								if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
									e.preventDefault();
									handleRunClick();
								}
							}}
						/>
					</div>

					<div className="flex items-center justify-between px-3 pt-1">
						<TooltipProvider>
							<Tooltip delayDuration={0}>
								<TooltipTrigger asChild>
									<Button size="icon" className="rounded-full border-none" onClick={onUpload} title="Upload images">
										<PlusIcon className="h-4 w-4" />
									</Button>
								</TooltipTrigger>
								<TooltipContent>
									<span>Upload Image</span>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>

						<div className="flex items-center gap-2">
							<Button
								onClick={handleRunClick}
								size="icon"
								disabled={isGenerating || !(generationSettings.prompt.trim() && selectedIds.length > 0)}
								className={cn("gap-2 font-medium transition-all")}
							>
								{isGenerating ? (
									<LoaderCircleIcon className="h-4 w-4 animate-spin text-white" />
								) : (
									<ZapIcon className="h-4 w-4 fill-white text-white" />
								)}
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
