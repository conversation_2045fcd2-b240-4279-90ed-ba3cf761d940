import React from "react";
import { ContextMenuContent, ContextMenuItem, ContextMenuSeparator } from "@/components/ui/context-menu";
import type { PlacedImage } from "@/@types/canvas";
import { checkOS } from "@/lib/utils-os";
import { downloadImages } from "@/lib/file/utils-canvas-download";
import { toast } from "sonner";

interface CanvasContextMenuProps {
	selectedIds: string[];
	images: PlacedImage[];
	handleCombineImages: () => void;
	handleDelete: () => void;
	sendToFront: () => void;
	sendToBack: () => void;
	bringForward: () => void;
	sendBackward: () => void;
	onZoomIn?: () => void;
	onZoomOut?: () => void;
	onZoomToFit?: () => void;
}

export const CanvasContextMenu: React.FC<CanvasContextMenuProps> = ({
	selectedIds,
	images,
	handleCombineImages,
	handleDelete,
	sendTo<PERSON>ront,
	sendToBack,
	bringForward,
	sendBackward,
	onZoomIn,
	onZoomOut,
	onZoomToFit,
}) => {
	const isMac = checkOS("Mac");
	const modifierKey = isMac ? "⌘" : "Ctrl";

	// Don't show image-specific actions if no images are selected
	const hasSelectedImages = selectedIds.length > 0;
	const hasMultipleSelected = selectedIds.length > 1;

	return (
		<ContextMenuContent className="min-w-[200px] space-y-1 p-2">
			{/* Only show Group for multiple selected images. 暂时注释掉，不开放，以后再开放 */}
			{/* {hasMultipleSelected && (
				<>
					<ContextMenuItem
						onClick={handleCombineImages}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs text-neutral-600"
					>
						<span>Group</span>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">G</div>
						</div>
					</ContextMenuItem>
					<ContextMenuSeparator />
				</>
			)} */}
			{hasSelectedImages && (
				<>
					<ContextMenuItem
						onClick={bringForward}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Move up</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"]"}</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem
						onClick={sendBackward}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Move down</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"["}</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem
						onClick={sendToFront}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Bring to front</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"]"}</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem
						onClick={sendToBack}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Send to back</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"["}</div>
						</div>
					</ContextMenuItem>
				</>
			)}

			{/* Zoom controls - only show when no images are selected */}
			{!hasSelectedImages && (
				<>
					<ContextMenuItem onClick={onZoomIn} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom in</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">+</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem onClick={onZoomOut} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom out</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">-</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem onClick={onZoomToFit} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom to fit</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">⇧</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">1</div>
						</div>
					</ContextMenuItem>
				</>
			)}

			{/* Download and Delete - only show when images are selected */}
			{hasSelectedImages && (
				<>
					<ContextMenuSeparator />
					<ContextMenuItem
						onClick={async () => {
							try {
								const imagesToDownload = selectedIds
									.map((id) => images.find((img) => img.id === id))
									.filter((img): img is PlacedImage => img !== undefined)
									.map((img, index) => ({
										src: img.src,
										fileName: `image-${Date.now()}-${index + 1}`,
									}));

								if (imagesToDownload.length > 0) {
									await downloadImages(imagesToDownload);
								}
							} catch (error) {
								console.error("Download failed:", error);
								toast.error("Download failed");
							}
						}}
						className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600"
					>
						Download
					</ContextMenuItem>
					<ContextMenuItem onClick={handleDelete} className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600">
						Delete
					</ContextMenuItem>
				</>
			)}
		</ContextMenuContent>
	);
};
