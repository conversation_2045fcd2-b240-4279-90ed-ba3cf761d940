import React, { useRef, useState, useEffect, useMemo } from "react";
import { Image as KonvaImage, Transformer, Rect, Text } from "react-konva";
import Konva from "konva";
import useImage from "use-image";
import { useStreamingImage } from "@/hooks/useStreamingImage";
import type { PlacedImage } from "@/@types/canvas";
import { throttle } from "@/lib/utils-performance";

interface CanvasImageProps {
	image: PlacedImage;
	isSelected: boolean;
	onSelect: (e: Konva.KonvaEventObject<MouseEvent>) => void;
	onChange: (newAttrs: Partial<PlacedImage>) => void;
	onDragStart: () => void;
	onDragEnd: () => void;
	onDoubleClick?: () => void;
	selectedIds: string[];
	images: PlacedImage[];
	setImages: React.Dispatch<React.SetStateAction<PlacedImage[]>>;
	isDraggingImage: boolean;
	isCroppingImage: boolean;
	dragStartPositions: Map<string, { x: number; y: number }>;
}

export const CanvasImage: React.FC<CanvasImageProps> = React.memo(
	({
		image,
		isSelected,
		onSelect,
		onChange,
		onDragStart,
		onDragEnd,
		onDoubleClick,
		selectedIds,
		images,
		setImages,
		isDraggingImage,
		isCroppingImage,
		dragStartPositions,
	}) => {
		const shapeRef = useRef<Konva.Image>(null);
		const trRef = useRef<Konva.Transformer>(null);
		// Use streaming image hook for generated images to prevent flicker
		const [streamingImg] = useStreamingImage(image.isGenerated ? image.src : "");
		const [normalImg] = useImage(image.isGenerated ? "" : image.src, "anonymous");
		const img = image.isGenerated ? streamingImg : normalImg;
		const [isHovered, setIsHovered] = useState(false);
		const [isDraggable, setIsDraggable] = useState(true);
		const skeletonRef = useRef<any>(null);
		const badgeRef = useRef<any>(null);

		// 生成中的Skeleton动画效果 - 使用Konva Animation
		useEffect(() => {
			if (image.status === "generating" && skeletonRef.current) {
				const skeleton = skeletonRef.current;
				let isActive = true;

				const anim = new Konva.Animation((frame) => {
					if (!frame || !isActive || !skeleton) return;
					// 使用正弦波创建平滑的呼吸效果
					const opacity = 0.1 + (0.3 * (Math.sin(frame.time * 0.003) + 1)) / 2;
					skeleton.opacity(opacity);
				}, skeleton.getLayer());

				anim.start();

				return () => {
					isActive = false;
					anim.stop();
				};
			}
		}, [image.status]);

		// Badge脉冲动画效果
		useEffect(() => {
			if (image.status === "generating" && badgeRef.current) {
				const badge = badgeRef.current;
				let tween1: Konva.Tween | null = null;
				let tween2: Konva.Tween | null = null;
				let isActive = true;

				const createPulseAnimation = () => {
					if (!isActive || !badge) return;

					tween1 = new Konva.Tween({
						node: badge,
						duration: 1.5,
						scaleX: 1.1,
						scaleY: 1.1,
						opacity: 0.8,
						easing: Konva.Easings.EaseInOut,
						onFinish: () => {
							if (!isActive || !badge) return;

							tween2 = new Konva.Tween({
								node: badge,
								duration: 1.5,
								scaleX: 1,
								scaleY: 1,
								opacity: 1,
								easing: Konva.Easings.EaseInOut,
								onFinish: () => {
									if (isActive && image.status === "generating") {
										createPulseAnimation(); // 递归循环
									}
								},
							});
							tween2.play();
						},
					});
					tween1.play();
				};

				createPulseAnimation();

				return () => {
					isActive = false;
					if (tween1) {
						tween1.destroy();
					}
					if (tween2) {
						tween2.destroy();
					}
				};
			}
		}, [image.status]);

		useEffect(() => {
			if (isSelected && trRef.current && shapeRef.current) {
				// Only show transformer if this is the only selected item or if clicking on it
				if (selectedIds.length === 1) {
					trRef.current.nodes([shapeRef.current]);
					trRef.current.getLayer()?.batchDraw();
				} else {
					trRef.current.nodes([]);
				}
			}
		}, [isSelected, selectedIds.length]);

		return (
			<>
				<KonvaImage
					ref={shapeRef}
					id={image.id}
					image={img}
					x={image.x}
					y={image.y}
					width={image.width}
					height={image.height}
					rotation={image.rotation}
					crop={
						image.cropX !== undefined && !isCroppingImage
							? {
									x: (image.cropX || 0) * (img?.naturalWidth || 0),
									y: (image.cropY || 0) * (img?.naturalHeight || 0),
									width: (image.cropWidth || 1) * (img?.naturalWidth || 0),
									height: (image.cropHeight || 1) * (img?.naturalHeight || 0),
								}
							: undefined
					}
					draggable={isDraggable}
					onClick={onSelect}
					onTap={onSelect}
					onDblClick={onDoubleClick}
					onDblTap={onDoubleClick}
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
					onMouseDown={(e) => {
						// Only allow dragging with left mouse button (0)
						// Middle mouse (1) and right mouse (2) should not drag images
						const isLeftButton = e.evt.button === 0;
						setIsDraggable(isLeftButton);

						// For middle mouse button, don't stop propagation
						// Let it bubble up to the stage for canvas panning
						if (e.evt.button === 1) {
							return;
						}
					}}
					onMouseUp={() => {
						// Re-enable dragging after mouse up
						setIsDraggable(true);
					}}
					onDragStart={(e) => {
						// Stop propagation to prevent stage from being dragged
						e.cancelBubble = true;
						// Auto-select on drag if not already selected
						if (!isSelected) {
							onSelect(e);
						}
						onDragStart();
					}}
					onDragMove={useMemo(
						() =>
							throttle((e: any) => {
								const node = e.target;

								if (selectedIds.includes(image.id) && selectedIds.length > 1) {
									// Calculate delta from drag start position
									const startPos = dragStartPositions.get(image.id);
									if (startPos) {
										const deltaX = node.x() - startPos.x;
										const deltaY = node.y() - startPos.y;

										// Update all selected items relative to their start positions
										setImages((prev) =>
											prev.map((img) => {
												if (img.id === image.id) {
													return {
														...img,
														x: node.x(),
														y: node.y(),
													};
												} else if (selectedIds.includes(img.id)) {
													const imgStartPos = dragStartPositions.get(img.id);
													if (imgStartPos) {
														return {
															...img,
															x: imgStartPos.x + deltaX,
															y: imgStartPos.y + deltaY,
														};
													}
												}
												return img;
											}),
										);
									}
								} else {
									onChange({
										x: node.x(),
										y: node.y(),
									});
								}
							}, 16), // ~60fps throttle, prevents Safari console errors
						[selectedIds, image.id, dragStartPositions, setImages, onChange],
					)}
					onDragEnd={(e) => {
						onDragEnd();
					}}
					onTransformStart={() => {
						// Save to history before starting transform operation
						onDragStart();
					}}
					onTransformEnd={() => {
						const node = shapeRef.current;
						if (node) {
							const scaleX = node.scaleX();
							const scaleY = node.scaleY();

							node.scaleX(1);
							node.scaleY(1);

							onChange({
								x: node.x(),
								y: node.y(),
								width: Math.max(5, node.width() * scaleX),
								height: Math.max(5, node.height() * scaleY),
								rotation: node.rotation(),
							});
						}
						onDragEnd();
					}}
					opacity={1}
					stroke={isSelected ? "#3b82f6" : isHovered ? "#3b82f6" : "transparent"}
					strokeWidth={isSelected ? (selectedIds.length > 1 ? 4 : 2) : isHovered ? 2 : 0}
				/>
				{isSelected && selectedIds.length === 1 && (
					<Transformer
						ref={trRef}
						boundBoxFunc={(oldBox, newBox) => {
							if (newBox.width < 5 || newBox.height < 5) {
								return oldBox;
							}
							// Maintain aspect ratio
							const aspectRatio = oldBox.width / oldBox.height;
							if (newBox.width / newBox.height !== aspectRatio) {
								// Adjust height to maintain aspect ratio
								newBox.height = newBox.width / aspectRatio;
							}
							return newBox;
						}}
						enabledAnchors={["top-left", "top-right", "bottom-left", "bottom-right"]} // Show four corner anchors for proportional scaling
						rotateEnabled={false} // Disable rotation
						flipEnabled={false} // Disable flipping
						keepRatio={true} // Keep aspect ratio during transform
					/>
				)}

				{/* 生成中状态指示器 */}
				{image.status === "generating" && (
					<>
						{/* 半透明遮罩 - 带Skeleton效果 */}
						<Rect x={image.x} y={image.y} width={image.width} height={image.height} fill="rgba(0, 0, 0, 0.4)" cornerRadius={8} />

						{/* Skeleton效果的渐变遮罩 */}
						<Rect
							ref={skeletonRef}
							x={image.x}
							y={image.y}
							width={image.width}
							height={image.height}
							fill="rgba(255, 255, 255, 0.1)"
							opacity={0.3}
							cornerRadius={8}
						/>

						{/* Badge样式的生成中指示器 - 底部中间 */}
						<Rect
							ref={badgeRef}
							x={image.x + image.width / 2 - 90} // 居中，Badge宽度120的一半
							y={image.y + image.height - 48 - 36} // 距离底部24px
							width={264}
							height={64}
							fill="rgba(45, 45, 45, 0.95)"
							cornerRadius={48}
							shadowColor="rgba(0, 0, 0, 0.4)"
							shadowBlur={8}
							shadowOffset={{ x: 0, y: 2 }}
						/>

						{/* Badge文字 */}
						<Text
							x={image.x + image.width / 2 - 24} // 图片中心X坐标
							y={image.y + image.height - 64} // Badge中心Y坐标
							text="Generating..."
							fontSize={36}
							fontFamily="Arial, sans-serif"
							fill="white"
							align="center"
							verticalAlign="middle"
							offsetX={42} // 文字宽度的一半，用于居中
							offsetY={7} // 文字高度的一半，用于居中
							// fontStyle="bold"
						/>
					</>
				)}
			</>
		);
	},
);
