import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { ImageComparison } from "../ui/custom/image-comparison-slider";

export function FeatureImageComparisonItem({
	title,
	description,
	ctaText,
	ctaUrl,
	beforeImage,
	beforeImageAlt,
	afterImage,
	afterImageAlt,
	index,
}: {
	title: string;
	description: string;
	ctaText: string;
	ctaUrl: string;
	beforeImage: string;
	beforeImageAlt: string;
	afterImage: string;
	afterImageAlt: string;
	index: number;
}) {
	return (
		<div key={index} className="container flex flex-col items-center gap-16 px-4 py-24 md:px-6">
			<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-20">
				<div
					className={cn("flex flex-col items-center gap-6 text-center md:items-start md:gap-8 md:text-start", index % 2 === 0 ? "md:order-last" : "")}
				>
					<div className="flex flex-col gap-6">
						<h2 className="text-3xl font-semibold text-balance">{title}</h2>
						<p className="text-secondary-foreground text-base">{description}</p>
					</div>
					<NoPrefetchLink href={ctaUrl} className={cn(buttonVariants(), "bg-secondary-foreground hover:bg-secondary-foreground/80 rounded-full")}>
						{ctaText}
					</NoPrefetchLink>
				</div>
				<div className="bg-muted block w-full overflow-hidden rounded-xl">
					<ImageComparison beforeImage={beforeImage} beforeImageAlt={beforeImageAlt} afterImage={afterImage} afterImageAlt={afterImageAlt} />
				</div>
			</div>
		</div>
	);
}
