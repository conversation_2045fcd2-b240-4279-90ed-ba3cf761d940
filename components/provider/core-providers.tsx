"use client";

import { ReactNode } from "react";
import { Toaster } from "sonner";
import SignInDialog from "../shared/sigin-in-dialog";
import PlanDialog from "../shared/plan-dialog";
import { AnalyticsClarity } from "../analytics/analytics-clarity";
import { AnalyticsGoogle } from "../analytics/analytics-google";
// import { CookiesProvider } from "next-client-cookies/server";
// import { ThemeProvider } from "next-themes";
import { QueryClientProvider } from "@tanstack/react-query";
import { getQueryClient } from "@/orpc/query-client";

export function CoreProviders({ children }: { children: ReactNode }) {
	const queryClient = getQueryClient();
	return (
		<QueryClientProvider client={queryClient}>
			{/* <CookiesProvider> */}
			{/* <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} forcedTheme="dark"> */}
			{children}
			<Toaster richColors position="top-center" />
			<SignInDialog />
			<PlanDialog />
			<AnalyticsClarity />
			<AnalyticsGoogle />
			{/* </ThemeProvider> */}
			{/* </CookiesProvider> */}
		</QueryClientProvider>
	);
}
