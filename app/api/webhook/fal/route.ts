import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { WEBNAME } from "@/lib/constants";
import { saveToR2 } from "@/server/r2.server";
import { eq } from "drizzle-orm";
import { MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import crypto from "crypto";
import sodium from "libsodium-wrappers";
import { handleApiError } from "@/@types/error-api";
import { getKVKeyMediaTask, getUUIDString } from "@/lib/utils";
import { deleteValue } from "@/server/kv/redis-upstash.server";

const JWKS_URL = "https://rest.alpha.fal.ai/.well-known/jwks.json";
const JWKS_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
let jwksCache: any[] | null = null;
let jwksCacheTime = 0;

// https://docs.fal.ai/model-endpoints/webhooks
type Params = {
	request_id: string;
	gateway_request_id: string;
	status: string; // "OK" | "ERROR"
	error?: string;
	payload: any | null;
	payload_error?: string;
};

export async function POST(req: Request): Promise<NextResponse> {
	return NextResponse.json({ message: "Webhook processed successfully" }, { status: 200 });
	// try {
	// 	// 1.Verify webhook signature
	// 	// header
	// 	const headers = req.headers;
	// 	const headerRequestId = headers.get("X-Fal-Webhook-Request-Id");
	// 	const headerUserId = headers.get("X-Fal-Webhook-User-Id");
	// 	const headerTimestamp = headers.get("X-Fal-Webhook-Timestamp");
	// 	const headerSignature = headers.get("X-Fal-Webhook-Signature");
	// 	if (process.env.NODE_ENV === "development") {
	// 		console.log("headerRequestId: ", headerRequestId);
	// 		console.log("headerUserId: ", headerUserId);
	// 		console.log("headerTimestamp: ", headerTimestamp);
	// 		console.log("headerSignature: ", headerSignature);
	// 	}
	// 	if (!headerRequestId || !headerUserId || !headerTimestamp || !headerSignature) {
	// 		console.error("Missing required webhook headers");
	// 		return NextResponse.json({ error: "Invalid webhook request" }, { status: 401 });
	// 	}
	// 	// Convert request body to Buffer for verification
	// 	const rawBody = await req.arrayBuffer();
	// 	const bodyBuffer = Buffer.from(rawBody);
	// 	const isValid = await verifyWebhookSignature(headerRequestId, headerUserId, headerTimestamp, headerSignature, bodyBuffer);
	// 	if (!isValid) {
	// 		console.error("Invalid webhook signature");
	// 		return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
	// 	}
	// 	// 2. Process the webhook data
	// 	let body: Params | null = null;
	// 	const bodyAsString = bodyBuffer.toString("utf-8");
	// 	try {
	// 		body = JSON.parse(bodyAsString);
	// 		if (!body) throw new Error("Failed to parse JSON body");
	// 	} catch (jsonParseError) {
	// 		console.error("Error parsing body as JSON:", jsonParseError);
	// 		return NextResponse.json({ message: "Invalid JSON body" }, { status: 400 });
	// 	}
	// 	if (process.env.NODE_ENV === "development") {
	// 		console.log("bodyText: ", JSON.stringify(body));
	// 	}
	// 	const requestId = body.request_id;
	// 	const db = getDB();
	// 	// 3. Check if the media task exists and is not completed
	// 	const [mediaTask]: MediaTask[] = await db.select().from(mediaTaskSchema).where(eq(mediaTaskSchema.requestId, requestId)).limit(1);
	// 	if (!mediaTask) {
	// 		console.error("Media task not found");
	// 		return NextResponse.json({ error: "Media task not found" }, { status: 404 });
	// 	}
	// 	if (mediaTask.status === MediaResultStatus.Completed || mediaTask.status === MediaResultStatus.Failed) {
	// 		console.error("Media task already completed");
	// 		return NextResponse.json({ error: "Media task already completed" }, { status: 200 });
	// 	}
	// 	const mediaType = mediaTask.type;
	// 	// Handle the webhook payload based on the status
	// 	if (body.status === "OK") {
	// 		let resultUrls: string[] = [];
	// 		if (mediaType === MediaHeadType.Image) resultUrls = body.payload.images.map((image: any) => image.url);
	// 		if (mediaType === MediaHeadType.Video) resultUrls = [body.payload.video.url];
	// 		if (process.env.NODE_ENV === "development") {
	// 			console.log("resultUrls: ", resultUrls);
	// 		}
	// 		// save to r2
	// 		const mediaPaths = await Promise.all(resultUrls.map((url) => saveToR2(url, mediaType)));
	// 		// save to db
	// 		const mediaResultId = getUUIDString();
	// 			await tx
	// 				.update(mediaTaskSchema)
	// 				.set({ status: MediaResultStatus.Completed, mediaHeadUid: mediaResultId })
	// 				.where(eq(mediaTaskSchema.requestId, requestId));
	// 		});
	// 		await deleteValue(getKVKeyMediaTask(mediaTask.requestId));
	// 	} else {
	// 		let errorMessage = body.error;
	// 		const detailMessage = body.payload?.detail?.[0]?.msg;
	// 		if (detailMessage) {
	// 			errorMessage = detailMessage;
	// 		}
	// 		if (body.payload.detail[0])
	// 			await db.update(mediaTaskSchema).set({ status: MediaResultStatus.Failed, error: errorMessage }).where(eq(mediaTaskSchema.requestId, requestId));
	// 		notifyDevEvent(`${WEBNAME} - fal request error`, `request id: ${requestId}`, `${body.error}${detailMessage ? `\n${detailMessage}` : ""}`, null);
	// 	}
	// 	return NextResponse.json({ success: true });
	// } catch (error) {
	// 	return handleApiError(error, `${WEBNAME} - /api/webhook/fal`);
	// }
}

async function fetchJwks() {
	const currentTime = Date.now();
	if (!jwksCache || currentTime - jwksCacheTime > JWKS_CACHE_DURATION) {
		const response = await fetch(JWKS_URL, { next: { revalidate: 3600 } });
		if (!response.ok) throw new Error(`JWKS fetch failed: ${response.status}`);
		jwksCache = (await response.json()).keys || [];
		jwksCacheTime = currentTime;
	}
	return jwksCache;
}

async function verifyWebhookSignature(requestId: string, userId: string, timestamp: string, signatureHex: string, body: Buffer): Promise<boolean> {
	/*
	 * Verify a webhook signature using provided headers and body.
	 *
	 * @param {string} requestId - Value of x-fal-webhook-request-id header.
	 * @param {string} userId - Value of x-fal-webhook-user-id header.
	 * @param {string} timestamp - Value of x-fal-webhook-timestamp header.
	 * @param {string} signatureHex - Value of x-fal-webhook-signature header (hex-encoded).
	 * @param {Buffer} body - Raw request body as a Buffer.
	 * @returns {Promise<boolean>} True if the signature is valid, false otherwise.
	 */
	await sodium.ready;

	// Validate timestamp (within ±30 minutes)
	// try {
	// 	const timestampInt = parseInt(timestamp, 10);
	// 	const currentTime = Math.floor(Date.now() / 1000);
	// 	if (Math.abs(currentTime - timestampInt) > 60 * 30) {
	// 		console.error("Timestamp is too old or in the future.");
	// 		return false;
	// 	}
	// } catch (e) {
	// 	console.error("Invalid timestamp format:", e);
	// 	return false;
	// }

	// Construct the message to verify
	try {
		const bodyHash = crypto.createHash("sha256").update(body).digest("hex");
		const messageToVerify = [requestId, userId, timestamp, bodyHash].join("\n");
		const messageBytes = Buffer.from(messageToVerify, "utf-8");

		// Decode signature
		const signatureBytes = Buffer.from(signatureHex, "hex");

		// Fetch public keys
		const publicKeysInfo = await fetchJwks();
		if (process.env.NODE_ENV === "development") {
			console.log("publicKeysInfo: ", publicKeysInfo);
		}
		if (!publicKeysInfo || !publicKeysInfo.length) {
			console.error("No public keys found in JWKS.");
			return false;
		}

		// Verify signature with each public key
		for (const keyInfo of publicKeysInfo) {
			try {
				const publicKeyB64Url = keyInfo.x;
				if (typeof publicKeyB64Url !== "string") continue;
				const publicKeyBytes = Buffer.from(publicKeyB64Url, "base64url");
				const isValid = sodium.crypto_sign_verify_detached(signatureBytes, messageBytes, publicKeyBytes);
				if (isValid) {
					return true;
				}
			} catch (e) {
				console.error("Verification failed with a key:", e);
				continue;
			}
		}

		console.error("Signature verification failed with all keys.");
		return false;
	} catch (e) {
		console.error("Error constructing message:", e);
		return false;
	}
}
