import type { Metadata } from "next";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import FAQsComponent from "@/components/landing/faqs";
import FinalCTA from "@/components/landing/final-cta";
import { FeaturesComponent } from "@/components/landing/features";
import { HeroVideo } from "@/components/landing/hero-video";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export const metadata: Metadata = {
	title: `AI Image Editor: Your AI Image Editing Pal | ${WEBNAME}`,
	description:
		"Image editing is now as simple as describing your idea. Our AI image editor creates flawless results while maintaining perfect consistency for any subject.",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-24">
			<div className="mb-12 px-4 pb-6 md:mb-14 md:px-6">
				<div className="container flex h-full flex-col px-0 pt-20">
					<div className="flex w-full grow flex-col items-center justify-center md:grow-0">
						<div className="mx-auto flex w-full flex-col items-center gap-y-8 text-center">
							<h1 className="text-4xl leading-9 font-semibold text-balance md:text-5xl md:leading-16 md:font-semibold">
								Your AI Image Editing Pal
								{/* AI Image Editor That Works Like a Pal */}
							</h1>
							<div className="text-secondary-foreground text-lg">
								{/* Edit and refine your images, maintaining consistency with simple prompts. */}
								The first image editor with true creative control and flawless consistency.
							</div>
							<Link
								href="/edit"
								className={cn(
									buttonVariants({ size: "lg", variant: "default" }),
									"mt-2 h-12 rounded-full bg-blue-500 px-10 text-lg hover:bg-blue-500/80 md:h-14 md:px-12 md:text-xl",
								)}
							>
								Start editing
							</Link>
						</div>
					</div>
				</div>
			</div>

			<div className="px-4 md:px-6">
				<div className="container max-w-5xl px-0">
					<HeroVideo />
				</div>
			</div>
		</main>
	);
}
