"use client";

import { useEffect, useState } from "react";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download } from "lucide-react";
import { MembershipID } from "@/@types/membership-type";
import { cn } from "@/lib/utils";
import { downloadImageFromBase64, downloadImageFromUrl, imageUrlToBase64 } from "@/lib/file/utils-file";
import { ofetch } from "ofetch";
import { ERROR_CODE, handleError } from "@/@types/error";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { MediaHeadType } from "@/@types/media/media-type";
import { useUser } from "@/hooks/use-userinfo";

export default function MyAssets({ isHasPaid }: { isHasPaid: boolean }) {
	const { data: userData } = useUser();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	// ======== Display media ========
	// Pagination
	const [page, setPage] = useState<number>(1);
	const [totalPage, setTotalPage] = useState<number>(1);
	const [totalCount, setTotalCount] = useState<number>(0);
	const pageSize = 20;
	// Fetch media
	const [mediaItems, setMediaItems] = useState<any[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const fetchMediaItems = async (currentPage: number) => {
		try {
			setIsLoading(true);
			const { status, message, mediaItems } = await ofetch(
				"/api/v1/user/my-assets",

				{
					method: "POST",
				},
			);
			handleError(status, message);
			setMediaItems(mediaItems as { uid: string; url: string; type: MediaHeadType }[]);
			// if (totalPage === 0) {
			// 	setTotalPage(1);
			// } else {
			// 	setTotalPage(totalPage);
			// }
			// setTotalCount(total);
		} catch (error: any) {
			console.error("Failed to fetch:", error);
			if (error.status === ERROR_CODE.UNAUTHORIZED) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Failed to fetch my creations.");
		} finally {
			setIsLoading(false);
		}
	};
	useEffect(() => {
		// if (!isHasPaid) return;
		fetchMediaItems(page);
	}, []);

	const [downloading, setDownloading] = useState(false);
	const [downloadingImageUid, setDownloadingImageUid] = useState<string>();

	return (
		<div className="min-h-screen px-4 py-4 md:px-6">
			<div className="container flex h-full w-full flex-col px-0">
				<div className="mx-auto flex w-full flex-col pt-4 pb-2">
					<h1 className="text-2xl font-medium whitespace-nowrap">My Assets</h1>
					<p className="text-muted-foreground text-sm">Showing your assets from the last 30 days</p>
				</div>

				{/* {!isHasPaid && (
				<div className="bg-muted mx-auto mt-20 flex max-w-[360px] flex-col items-center gap-4 rounded-lg p-6">
					<p className="text-muted-foreground">Upgrade to view your creations</p>
					<Button onClick={() => setPlanBoxOpen(true)} variant="outline" className="">
						✨ Upgrade Now
					</Button>
				</div>
			)} */}

				<div className="mx-auto grid w-full grid-cols-3 gap-2 pt-4 sm:grid-cols-4 lg:grid-cols-6">
					{isLoading ? (
						<>
							{Array.from({ length: 12 }).map((_, index) => (
								<div key={index} className="group relative aspect-square w-full rounded">
									<Skeleton className="bg-muted aspect-square h-full w-full rounded object-contain" />
								</div>
							))}
						</>
					) : (
						mediaItems.map((item, index) => (
							<div key={index} className="group relative aspect-square w-full rounded">
								{item.type === MediaHeadType.Image && (
									<img
										src={item.url}
										alt=""
										className="aspect-square h-full w-full rounded object-contain"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
										loading="lazy"
									/>
								)}
								{item.type === MediaHeadType.Video && (
									<video
										src={item.url}
										muted
										className="aspect-square h-full w-full rounded object-contain"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
										onMouseEnter={(e) => (e.target as HTMLVideoElement).play()}
										onMouseLeave={(e) => (e.target as HTMLVideoElement).pause()}
										preload="metadata"
									/>
								)}
								<div
									className={cn(
										"absolute right-2 bottom-2 items-center gap-1",
										downloadingImageUid === item.uid ? "opacity-100" : "group-hover:opacity-100 sm:opacity-0",
									)}
								>
									<SubmitButton
										isSubmitting={downloading && downloadingImageUid === item.uid}
										size="icon"
										className="bg-foreground hover:bg-foreground/80 shrink-0 cursor-pointer"
										onClick={async () => {
											try {
												setDownloadingImageUid(item.uid);
												setDownloading(true);
												if (item.type === MediaHeadType.Image) {
													const base64 = await imageUrlToBase64(item.url, {
														noCache: true,
														hasWatermark: userData?.hasPaid ? false : true,
													});
													await downloadImageFromBase64(base64);
												} else {
													await downloadImageFromUrl(item.url);
												}
											} catch (error) {
												console.error("Failed to download image:", error);
											} finally {
												setDownloadingImageUid(undefined);
												setDownloading(false);
											}
										}}
									>
										<Download />
									</SubmitButton>
								</div>
							</div>
						))
					)}
				</div>
			</div>
		</div>
	);
}
