"use client";

import React from "react";
import { useState } from "react";
import { Stage, Layer, Rect } from "react-konva";
import Kon<PERSON> from "konva";
import { Button } from "@/components/ui/button";
import { GemIcon } from "lucide-react";
import { useRef, useEffect } from "react";
import { ContextMenu, ContextMenuTrigger } from "@/components/ui/context-menu";
import { useSession } from "@/lib/auth-client";
import { useUser, useRefreshUser } from "@/hooks/use-userinfo";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { MembershipID } from "@/@types/membership-type";

// Import extracted components
import { CropOverlayWrapper } from "@/components/canvas/CropOverlayWrapper";
import { CanvasImage } from "@/components/canvas/CanvasImage";
import { useMutation } from "@tanstack/react-query";

// Import types
import type { PlacedImage } from "@/@types/canvas";

// Import additional extracted components
import { SelectionBoxComponent } from "@/components/canvas/SelectionBox";
import { ZoomControls } from "@/components/canvas/ZoomControls";
import { MobileToolbar } from "@/components/canvas/MobileToolbar";
import { TopToolbar } from "@/components/canvas/TopToolbar";
import { HeaderDropdown } from "@/components/canvas/HeaderDropdown";
import { CanvasContextMenu } from "@/components/canvas/CanvasContextMenu";
import { DimensionDisplay } from "@/components/canvas/DimensionDisplay";

// Import handlers
import { handleRun as handleRunHandler } from "@/lib/handlers/generation-handler";
import { handleRemoveBackground as handleRemoveBackgroundHandler, handleUpscaleHandler } from "@/lib/handlers/background-handler";
import { toast } from "sonner";
import { orpc } from "@/orpc/client";
import { uploadFile } from "@/lib/file/upload-file";
import { ERROR_CODE } from "@/@types/error";
import { useRouter } from "nextjs-toploader/app";
import { UserMenu } from "@/components/auth/UserMenu";
import { PromptBar } from "@/components/canvas/layout/PromptBar";
import { InitialView } from "@/components/canvas/layout/InitialView";
import { useKeyboardShortcuts } from "@/lib/hooks/canvas/useKeyboardShortcuts";
import { useCanvasInteraction } from "@/lib/hooks/canvas/useCanvasInteraction";
import { useLayerActions } from "@/lib/hooks/canvas/useLayerActions";
import { useStoragePersistence } from "@/lib/hooks/canvas/useStoragePersistence";
import { useCanvasState } from "@/lib/hooks/canvas/useCanvasState";
import { createCroppedImage } from "@/lib/hooks/canvas/useImageOperations";

export default function EditCanvas() {
	const router = useRouter();
	const { data: userData } = useUser();
	const refreshUser = useRefreshUser();
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const stageRef = useRef<Konva.Stage>(null);
	const [canvasSize, setCanvasSize] = useState({ width: 1200, height: 800 });
	const [hasUploadingImages, setHasUploadingImages] = useState(false);
	const [isGenerating, setIsGenerating] = useState(false);
	const [zoomFunctions, setZoomFunctions] = useState<{ handleZoomIn: () => void; handleZoomOut: () => void; handleZoomToFit: () => void } | null>(null);
	// Use a consistent initial value for server and client to avoid hydration errors
	const [isCanvasReady, setIsCanvasReady] = useState(false);

	const {
		images,
		setImages,
		applyWithHistory,
		reset,
		undo,
		redo,
		canUndo,
		canRedo,
		selectedIds,
		setSelectedIds,
		viewport,
		setViewport,
		croppingImageId,
		setCroppingImageId,
		isDraggingImage,
		setIsDraggingImage,
		dragStartPositions,
		setDragStartPositions,
	} = useCanvasState({ hasUploadingImages });

	const { mutateAsync: generateTextToImage } = useMutation(orpc.image.generateTextToImage.mutationOptions());
	const { mutateAsync: editImage } = useMutation(orpc.image.editImage.mutationOptions());
	const { mutateAsync: removeBackground } = useMutation(orpc.imageTool.removeBackground.mutationOptions());
	const { mutateAsync: upscaleImage } = useMutation(orpc.imageTool.upscale.mutationOptions());

	const { showInitialView, setShowInitialView } = useStoragePersistence({
		images,
		setImages,
		viewport,
		setViewport,
		resetHistory: reset,
		hasUploadingImages,
	});

	// Set canvas ready state after mount
	useEffect(() => {
		// Only set canvas ready after we have valid dimensions
		if (canvasSize.width > 0 && canvasSize.height > 0) {
			setIsCanvasReady(true);
		}
	}, [canvasSize]);

	// Update canvas size on window resize
	useEffect(() => {
		const updateCanvasSize = () => {
			setCanvasSize({ width: window.innerWidth, height: window.innerHeight });
		};
		updateCanvasSize();
		window.addEventListener("resize", updateCanvasSize);
		return () => window.removeEventListener("resize", updateCanvasSize);
	}, []);

	// Prevent body scrolling on mobile
	useEffect(() => {
		// Prevent scrolling on mobile
		document.body.style.overflow = "hidden";
		document.body.style.position = "fixed";
		document.body.style.width = "100%";
		document.body.style.height = "100%";

		return () => {
			document.body.style.overflow = "";
			document.body.style.position = "";
			document.body.style.width = "";
			document.body.style.height = "";
		};
	}, []);

	// Unified upload function that can be called from anywhere
	const triggerFileUpload = () => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}

		const input = document.createElement("input");
		input.type = "file";
		input.accept = "image/png, image/jpeg, image/jpg";
		input.multiple = true;

		// Add to DOM for mobile compatibility
		input.style.position = "fixed";
		input.style.top = "-1000px";
		input.style.left = "-1000px";
		input.style.opacity = "0";
		input.style.pointerEvents = "none";
		input.style.width = "1px";
		input.style.height = "1px";

		// Add event handlers
		input.onchange = async (e) => {
			try {
				setShowInitialView(false);
				await handleFileUpload((e.target as HTMLInputElement).files);
			} catch (error) {
				console.error("File upload error:", error);
				toast.error("Upload failed", {
					description: "Failed to process selected files",
				});
			} finally {
				// Clean up
				if (input.parentNode) {
					document.body.removeChild(input);
				}
			}
		};
		input.onerror = () => {
			console.error("File input error");
			if (input.parentNode) {
				document.body.removeChild(input);
			}
		};
		// Add to DOM and trigger
		document.body.appendChild(input);

		// Use setTimeout to ensure the input is properly attached
		setTimeout(() => {
			try {
				input.click();
			} catch (error) {
				console.error("Failed to trigger file dialog:", error);
				toast.error("Upload unavailable", {
					description: "File upload is not available. Try using drag & drop instead.",
				});
				if (input.parentNode) {
					document.body.removeChild(input);
				}
			}
		}, 10);

		// Cleanup after timeout in case dialog was cancelled
		setTimeout(() => {
			if (input.parentNode) {
				document.body.removeChild(input);
			}
		}, 30000); // 30 second cleanup
	};

	// Handle file upload with optimistic UI update
	const handleFileUpload = async (files: FileList | null, position?: { x: number; y: number }) => {
		if (!files) return;

		const fileArray = Array.from(files).filter((file) => file.type.startsWith("image/"));
		if (fileArray.length === 0) return;

		try {
			setHasUploadingImages(true);

			// Process all files and collect the new images
			const newImages: PlacedImage[] = [];
			const uploadPromises: Array<{ promise: Promise<any>; id: string; file: File }> = [];

			for (let index = 0; index < fileArray.length; index++) {
				const file = fileArray[index];

				// Read file as data URL
				const dataUrl = await new Promise<string>((resolve, reject) => {
					const reader = new FileReader();
					reader.onload = (e) => resolve(e.target?.result as string);
					reader.onerror = reject;
					reader.readAsDataURL(file);
				});

				// Load image to get dimensions
				const { width, height } = await new Promise<{ width: number; height: number }>((resolve, reject) => {
					const img = new window.Image();
					img.crossOrigin = "anonymous";
					img.onload = () => resolve({ width: img.width, height: img.height });
					img.onerror = reject;
					img.src = dataUrl;
				});

				// Calculate position
				let x, y;
				if (position) {
					// Convert screen position to canvas coordinates
					x = (position.x - viewport.x) / viewport.scale - width / 2;
					y = (position.y - viewport.y) / viewport.scale - height / 2;
				} else {
					// Center of viewport
					const viewportCenterX = (canvasSize.width / 2 - viewport.x) / viewport.scale;
					const viewportCenterY = (canvasSize.height / 2 - viewport.y) / viewport.scale;
					x = viewportCenterX - width / 2;
					y = viewportCenterY - height / 2;
				}

				// Add offset for multiple files
				if (index > 0) {
					x += index * 20;
					y += index * 20;
				}

				const id = `img-${Date.now()}-${Math.random()}-${index}`;
				const newImage: PlacedImage = {
					id,
					src: dataUrl,
					x,
					y,
					width,
					height,
					rotation: 0,
					status: "uploading",
				};

				newImages.push(newImage);

				// Collect upload promise for batch processing
				const uploadPromise = uploadFile(file, true)
					.then(({ file_url }) => ({
						success: true,
						id,
						ossUrl: file_url,
					}))
					.catch((error) => ({
						success: false,
						id,
						error,
					}));

				uploadPromises.push({ promise: uploadPromise, id, file });
			}

			// Add all new images at once (without saving to history yet)
			if (newImages.length > 0) {
				setImages((prev) => [...prev, ...newImages]);
			}

			// Wait for all uploads to complete
			const results = await Promise.all(uploadPromises.map(({ promise }) => promise));

			// Process results in batch and update state atomically
			const successfulUploads: string[] = [];
			const failedUploads: string[] = [];
			let hasUnauthorizedError = false;

			results.forEach((result) => {
				if (result.success) {
					successfulUploads.push(result.id);
				} else {
					failedUploads.push(result.id);
					console.error("Failed to upload image:", result.error);

					// Check for unauthorized error
					if (result.error.status === ERROR_CODE.UNAUTHORIZED) {
						hasUnauthorizedError = true;
					}
				}
			});

			// 统一更新状态：更新成功的图片状态，移除失败的图片，并保存历史记录
			if (successfulUploads.length > 0) {
				applyWithHistory(
					(prev) =>
						prev
							.map((img) => {
								// 更新成功上传的图片
								const result = results.find((r) => r.success && r.id === img.id);
								if (result) {
									return {
										...img,
										status: "completed" as const,
										ossUrl: result.ossUrl,
									};
								}
								return img;
							})
							.filter((img) => !failedUploads.includes(img.id)), // 移除失败的图片
					"upload",
				);
			} else if (failedUploads.length > 0) {
				// 如果没有成功上传的图片，只移除失败的图片，不保存历史记录
				setImages((prev) => prev.filter((img) => !failedUploads.includes(img.id)));
			}

			// Show appropriate notifications
			if (hasUnauthorizedError) {
				setSignInBoxOpen(true);
			} else {
				// Show only one notification: prioritize failure over success
				if (failedUploads.length > 0) {
					// Show failure notification with context about successful uploads if any
					if (successfulUploads.length > 0) {
						const message = `${successfulUploads.length} uploaded, ${failedUploads.length} failed`;
						const description = `${successfulUploads.length} images uploaded successfully, but ${failedUploads.length} failed to upload`;
						toast.error(message, { description });
					} else {
						// All uploads failed
						const message = failedUploads.length === 1 ? "Upload failed" : `${failedUploads.length} images failed to upload`;
						const description = failedUploads.length === 1 ? "Failed to upload image" : `${failedUploads.length} images could not be uploaded`;
						toast.error(message, { description });
					}
				} else if (successfulUploads.length > 0) {
					// All uploads succeeded
					const message = successfulUploads.length === 1 ? "Image uploaded successfully" : `${successfulUploads.length} images uploaded successfully`;
					toast.success(message);
				}
			}
		} catch (error) {
			console.error("File upload error:", error);
			toast.error("Upload failed", {
				description: "An unexpected error occurred during file upload",
			});
			// 如果有上传中的图片，移除它们
			setImages((prev) => prev.filter((img) => img.status !== "uploading"));
		} finally {
			setHasUploadingImages(false);
		}
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();

		if (!session) {
			setSignInBoxOpen(true);
			return;
		}

		// Get drop position relative to the stage
		const stage = stageRef.current;
		if (stage) {
			const container = stage.container();
			const rect = container.getBoundingClientRect();
			const dropPosition = {
				x: e.clientX - rect.left,
				y: e.clientY - rect.top,
			};
			handleFileUpload(e.dataTransfer.files, dropPosition);
		} else {
			handleFileUpload(e.dataTransfer.files);
		}
	};

	// 鼠标移动画布
	const {
		handleMouseDown,
		handleMouseMove,
		handleMouseUp,
		handleTouchStart,
		handleTouchMove,
		handleTouchEnd,
		handleWheel,
		selectionBox,
		isPanningCanvas,
		setIsPanningCanvas,
	} = useCanvasInteraction({
		viewport,
		setViewport,
		croppingImageId,
		setCroppingImageId,
		images,
		setSelectedIds,
		stageRef,
		isDraggingImage,
	});

	// Handle selection
	const handleSelect = (id: string, e: Konva.KonvaEventObject<MouseEvent>) => {
		if (e.evt.shiftKey || e.evt.metaKey || e.evt.ctrlKey) {
			setSelectedIds((prev) => (prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]));
		} else {
			setSelectedIds([id]);
		}
	};

	// Handle context menu actions
	const handleRun = async (generationSettings: any) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		try {
			await handleRunHandler({
				images,
				selectedIds,
				generationSettings,
				canvasSize,
				viewport,
				setImages,
				setSelectedIds,
				setIsGenerating,
				generateTextToImage,
				editImage,
				applyWithHistory,
				membershipLevel: userData?.membershipLevel,
				refreshUser,
			});
		} catch (error: any) {
			console.error("Failed to generate image:", error);
			if (error.status === ERROR_CODE.UNAUTHORIZED) {
				setSignInBoxOpen(true);
				return;
			}
			if (error.status === ERROR_CODE.PAYMENT_REQUIRED) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: userData?.hasPaid ? "My billing" : "Get more",
						onClick: () => {
							if (userData?.hasPaid) {
								router.push("/my-billing");
							} else {
								setPlanBoxOpen(true);
							}
						},
					},
				});
				return;
			}
			toast.error("Generation failed", {
				description: error instanceof Error ? error.message : "Failed to generate image",
			});
		}
	};

	const handleDelete = () => {
		if (selectedIds.length === 0) return;
		applyWithHistory((prev) => prev.filter((img) => !selectedIds.includes(img.id)), "delete");
		setSelectedIds([]);
	};

	const handleDuplicate = () => {
		if (selectedIds.length === 0) return;
		const selectedImages = images.filter((img) => selectedIds.includes(img.id));
		const newImages = selectedImages.map((img) => ({
			...img,
			id: `img-${Date.now()}-${Math.random()}`,
			x: img.x + 20,
			y: img.y + 20,
		}));

		// Update both arrays
		applyWithHistory((prev) => [...prev, ...newImages], "duplicate");

		// Select all duplicated items
		const newIds = [...newImages.map((img) => img.id)];
		setSelectedIds(newIds);
	};
	const handleRemoveBackground = async () => {
		await handleRemoveBackgroundHandler({
			images,
			selectedIds,
			setImages,
			applyWithHistory,
			removeBackground,
			membershipLevel: userData?.membershipLevel,
			refreshUser,
		});
	};
	const handleUpscale = async () => {
		await handleUpscaleHandler({
			images,
			selectedIds,
			setImages,
			applyWithHistory,
			upscaleImage,
			membershipLevel: userData?.membershipLevel,
			refreshUser,
		});
	};

	const { sendToFront, sendToBack, bringForward, sendBackward } = useLayerActions(selectedIds, applyWithHistory);

	const handleCombineImages = async () => {
		if (selectedIds.length < 2) return;

		// Get selected images and sort by layer order
		const selectedImages = selectedIds.map((id) => images.find((img) => img.id === id)).filter((img) => img !== undefined) as PlacedImage[];

		const sortedImages = [...selectedImages].sort((a, b) => {
			const indexA = images.findIndex((img) => img.id === a.id);
			const indexB = images.findIndex((img) => img.id === b.id);
			return indexA - indexB;
		});

		// Load all images to calculate scale factors
		const imageElements: {
			img: PlacedImage;
			element: HTMLImageElement;
			scale: number;
		}[] = [];
		let maxScale = 1;

		for (const img of sortedImages) {
			const imgElement = new window.Image();
			imgElement.crossOrigin = "anonymous"; // Enable CORS
			imgElement.src = img.src;
			await new Promise((resolve) => {
				imgElement.onload = resolve;
			});

			// Calculate scale factor (original size / display size)
			// Account for crops if they exist
			const effectiveWidth = img.cropWidth ? imgElement.naturalWidth * img.cropWidth : imgElement.naturalWidth;
			const effectiveHeight = img.cropHeight ? imgElement.naturalHeight * img.cropHeight : imgElement.naturalHeight;

			const scaleX = effectiveWidth / img.width;
			const scaleY = effectiveHeight / img.height;
			const scale = Math.min(scaleX, scaleY); // Use min to maintain aspect ratio

			maxScale = Math.max(maxScale, scale);
			imageElements.push({ img, element: imgElement, scale });
		}

		// Use a reasonable scale - not too large to avoid memory issues
		const optimalScale = Math.min(maxScale, 4); // Cap at 4x to prevent huge images

		// Calculate bounding box of all selected images
		let minX = Infinity,
			minY = Infinity;
		let maxX = -Infinity,
			maxY = -Infinity;

		sortedImages.forEach((img) => {
			minX = Math.min(minX, img.x);
			minY = Math.min(minY, img.y);
			maxX = Math.max(maxX, img.x + img.width);
			maxY = Math.max(maxY, img.y + img.height);
		});

		const combinedWidth = maxX - minX;
		const combinedHeight = maxY - minY;

		// Create canvas at higher resolution
		const canvas = document.createElement("canvas");
		const ctx = canvas.getContext("2d");
		if (!ctx) {
			console.error("Failed to get canvas context");
			return;
		}

		canvas.width = Math.round(combinedWidth * optimalScale);
		canvas.height = Math.round(combinedHeight * optimalScale);

		console.log(`Creating combined image at ${canvas.width}x${canvas.height} (scale: ${optimalScale.toFixed(2)}x)`);

		// Draw each image in order using the pre-loaded elements
		for (const { img, element: imgElement } of imageElements) {
			// Calculate position relative to the combined canvas, scaled up
			const relX = (img.x - minX) * optimalScale;
			const relY = (img.y - minY) * optimalScale;
			const scaledWidth = img.width * optimalScale;
			const scaledHeight = img.height * optimalScale;

			ctx.save();

			// Handle rotation if needed
			if (img.rotation) {
				ctx.translate(relX + scaledWidth / 2, relY + scaledHeight / 2);
				ctx.rotate((img.rotation * Math.PI) / 180);
				ctx.drawImage(imgElement, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
			} else {
				// Handle cropping if exists
				if (img.cropX !== undefined && img.cropY !== undefined && img.cropWidth !== undefined && img.cropHeight !== undefined) {
					ctx.drawImage(
						imgElement,
						img.cropX * imgElement.naturalWidth,
						img.cropY * imgElement.naturalHeight,
						img.cropWidth * imgElement.naturalWidth,
						img.cropHeight * imgElement.naturalHeight,
						relX,
						relY,
						scaledWidth,
						scaledHeight,
					);
				} else {
					ctx.drawImage(imgElement, 0, 0, imgElement.naturalWidth, imgElement.naturalHeight, relX, relY, scaledWidth, scaledHeight);
				}
			}

			ctx.restore();
		}

		// Convert to blob and create data URL
		const blob = await new Promise<Blob>((resolve) => {
			canvas.toBlob((blob) => resolve(blob!), "image/png");
		});

		const reader = new FileReader();
		const dataUrl = await new Promise<string>((resolve) => {
			reader.onload = (e) => resolve(e.target?.result as string);
			reader.readAsDataURL(blob);
		});

		// Create new combined image
		const combinedImage: PlacedImage = {
			id: `combined-${Date.now()}-${Math.random()}`,
			src: dataUrl,
			x: minX,
			y: minY,
			width: combinedWidth,
			height: combinedHeight,
			rotation: 0,
		};

		// Remove the original images and add the combined one
		applyWithHistory((prev) => [...prev.filter((img) => !selectedIds.includes(img.id)), combinedImage], "combine");

		// Select the new combined image
		setSelectedIds([combinedImage.id]);
	};

	useKeyboardShortcuts({
		undo,
		redo,
		handleDelete,
		handleDuplicate,
		selectAll: () => setSelectedIds(images.map((img) => img.id)),
		zoomIn: () => zoomFunctions?.handleZoomIn(),
		zoomOut: () => zoomFunctions?.handleZoomOut(),
		zoomToFit: () => zoomFunctions?.handleZoomToFit(),
		resetZoom: () => setViewport({ x: 0, y: 0, scale: 1 }),
		sendToFront,
		sendToBack,
		bringForward,
		sendBackward,
		exitCropMode: () => setCroppingImageId(null),
		canUndo,
		canRedo,
		hasSelection: selectedIds.length > 0,
		isCropping: !!croppingImageId,
	});

	return (
		<div
			className="text-foreground font-focal relative flex h-screen w-full flex-col overflow-hidden bg-neutral-100"
			style={{ height: "100dvh" }}
			onDrop={handleDrop}
			onDragOver={(e) => e.preventDefault()}
			onDragEnter={(e) => e.preventDefault()}
			onDragLeave={(e) => e.preventDefault()}
		>
			{/* Main content */}
			<main className="relative flex w-full flex-1 items-center justify-center">
				<div className="relative h-full w-full">
					<ContextMenu>
						<ContextMenuTrigger asChild>
							<div
								className="relative h-full w-full overflow-hidden bg-neutral-100"
								style={{
									// Use consistent style property names to avoid hydration errors
									height: `${canvasSize.height}px`,
									width: `${canvasSize.width}px`,
									minHeight: `${canvasSize.height}px`,
									minWidth: `${canvasSize.width}px`,
									cursor: isPanningCanvas ? "grabbing" : "default",
									WebkitTouchCallout: "none", // Add this for iOS
									touchAction: "none", // For touch devices
								}}
							>
								{isCanvasReady && (
									<Stage
										ref={stageRef}
										width={canvasSize.width}
										height={canvasSize.height}
										x={viewport.x}
										y={viewport.y}
										scaleX={viewport.scale}
										scaleY={viewport.scale}
										draggable={false}
										onDragStart={(e) => {
											e.evt?.preventDefault();
										}}
										onDragEnd={(e) => {
											e.evt?.preventDefault();
										}}
										onMouseDown={handleMouseDown}
										onMouseMove={handleMouseMove}
										onMouseUp={handleMouseUp}
										onMouseLeave={() => {
											// Stop panning if mouse leaves the stage
											if (isPanningCanvas) {
												setIsPanningCanvas(false);
											}
										}}
										onTouchStart={handleTouchStart}
										onTouchMove={handleTouchMove}
										onTouchEnd={handleTouchEnd}
										onContextMenu={(e) => {
											// Check if this is a forwarded event from a video overlay
											const videoId = (e.evt as any)?.videoId || (e as any)?.videoId;
											if (videoId) {
												// This is a right-click on a video
												if (!selectedIds.includes(videoId)) {
													setSelectedIds([videoId]);
												}
												return;
											}

											// Get clicked position
											const stage = e.target.getStage();
											if (!stage) return;

											const point = stage.getPointerPosition();
											if (!point) return;

											// Convert to canvas coordinates
											const canvasPoint = {
												x: (point.x - viewport.x) / viewport.scale,
												y: (point.y - viewport.y) / viewport.scale,
											};

											// Check if we clicked on an image (check in reverse order for top-most image)
											const clickedImage = [...images].reverse().find((img) => {
												// Simple bounding box check
												// TODO: Could be improved to handle rotation
												return (
													canvasPoint.x >= img.x &&
													canvasPoint.x <= img.x + img.width &&
													canvasPoint.y >= img.y &&
													canvasPoint.y <= img.y + img.height
												);
											});

											if (clickedImage) {
												if (!selectedIds.includes(clickedImage.id)) {
													// If clicking on unselected image, select only that image
													setSelectedIds([clickedImage.id]);
												}
												// If already selected, keep current selection for multi-select context menu
											}
										}}
										onWheel={handleWheel}
									>
										<Layer>
											{/* Selection box */}
											<SelectionBoxComponent selectionBox={selectionBox} />

											{/* Multi-selection bounding box */}
											{selectedIds.length > 1 &&
												(() => {
													const selectedImages = images.filter((img) => selectedIds.includes(img.id));
													if (selectedImages.length === 0) return null;

													// Calculate bounding box of all selected images
													let minX = Infinity,
														minY = Infinity;
													let maxX = -Infinity,
														maxY = -Infinity;

													selectedImages.forEach((img) => {
														minX = Math.min(minX, img.x);
														minY = Math.min(minY, img.y);
														maxX = Math.max(maxX, img.x + img.width);
														maxY = Math.max(maxY, img.y + img.height);
													});

													const padding = 4; // Add some padding around the selection
													return (
														<Rect
															x={minX - padding}
															y={minY - padding}
															width={maxX - minX + padding * 2}
															height={maxY - minY + padding * 2}
															stroke="#3b82f6"
															strokeWidth={3}
															dash={[10, 5]}
															fill="transparent"
														/>
													);
												})()}

											{/* Render images */}
											{images
												.filter((image) => {
													// Performance optimization: only render visible images
													const buffer = 100; // pixels buffer
													const viewBounds = {
														left: -viewport.x / viewport.scale - buffer,
														top: -viewport.y / viewport.scale - buffer,
														right: (canvasSize.width - viewport.x) / viewport.scale + buffer,
														bottom: (canvasSize.height - viewport.y) / viewport.scale + buffer,
													};

													return !(
														image.x + image.width < viewBounds.left ||
														image.x > viewBounds.right ||
														image.y + image.height < viewBounds.top ||
														image.y > viewBounds.bottom
													);
												})
												.map((image) => (
													<CanvasImage
														key={image.id}
														image={image}
														isSelected={selectedIds.includes(image.id)}
														onSelect={(e) => handleSelect(image.id, e)}
														onChange={(newAttrs) => {
															setImages((prev) =>
																prev.map((img) =>
																	img.id === image.id
																		? {
																				...img,
																				...newAttrs,
																			}
																		: img,
																),
															);
														}}
														onDoubleClick={() => {
															setCroppingImageId(image.id);
														}}
														onDragStart={() => {
															// If dragging a selected item in a multi-selection, keep the selection
															// If dragging an unselected item, select only that item
															let currentSelectedIds = selectedIds;
															if (!selectedIds.includes(image.id)) {
																currentSelectedIds = [image.id];
																setSelectedIds(currentSelectedIds);
															}

															setIsDraggingImage(true);
															// Save positions of all selected items for drag delta calculation
															const positions = new Map<
																string,
																{
																	x: number;
																	y: number;
																}
															>();
															currentSelectedIds.forEach((id) => {
																const img = images.find((i) => i.id === id);
																if (img) {
																	positions.set(id, {
																		x: img.x,
																		y: img.y,
																	});
																}
															});
															setDragStartPositions(positions);
														}}
														onDragEnd={() => {
															setIsDraggingImage(false);
															setDragStartPositions(new Map());
															// 拖拽结束后，使用applyWithHistory提交最终状态
															// 这会创建一个新的历史记录条目
															applyWithHistory((prev) => prev, "drag");
														}}
														selectedIds={selectedIds}
														images={images}
														setImages={setImages}
														isDraggingImage={isDraggingImage}
														isCroppingImage={croppingImageId === image.id}
														dragStartPositions={dragStartPositions}
													/>
												))}

											{/* Crop overlay */}
											{croppingImageId &&
												(() => {
													const croppingImage = images.find((img) => img.id === croppingImageId);
													if (!croppingImage) return null;

													return (
														<CropOverlayWrapper
															image={croppingImage}
															viewportScale={viewport.scale}
															onCropChange={(crop) => {
																setImages((prev) =>
																	prev.map((img) =>
																		img.id === croppingImageId
																			? {
																					...img,
																					...crop,
																				}
																			: img,
																	),
																);
															}}
															onCropEnd={async () => {
																// Apply crop to image dimensions
																if (croppingImage) {
																	const cropWidth = croppingImage.cropWidth || 1;
																	const cropHeight = croppingImage.cropHeight || 1;
																	const cropX = croppingImage.cropX || 0;
																	const cropY = croppingImage.cropY || 0;

																	try {
																		// Create the cropped image at full resolution
																		const croppedImageSrc = await createCroppedImage(
																			croppingImage.src,
																			cropX,
																			cropY,
																			cropWidth,
																			cropHeight,
																		);

																		applyWithHistory(
																			(prev) =>
																				prev.map((img) =>
																					img.id === croppingImageId
																						? {
																								...img,
																								// Replace with cropped image
																								src: croppedImageSrc,
																								// Update position to the crop area's top-left
																								x: img.x + cropX * img.width,
																								y: img.y + cropY * img.height,
																								// Update dimensions to match crop size
																								width: cropWidth * img.width,
																								height: cropHeight * img.height,
																								// Remove crop values completely
																								cropX: undefined,
																								cropY: undefined,
																								cropWidth: undefined,
																								cropHeight: undefined,
																							}
																						: img,
																				),
																			"crop",
																		);
																	} catch (error) {
																		console.error("Failed to create cropped image:", error);
																	}
																}

																setCroppingImageId(null);
															}}
														/>
													);
												})()}
										</Layer>
									</Stage>
								)}

								{/* Initial view when no images */}
								{showInitialView && <InitialView onUpload={triggerFileUpload} isUploading={hasUploadingImages} />}
							</div>
						</ContextMenuTrigger>
						<CanvasContextMenu
							selectedIds={selectedIds}
							images={images}
							handleCombineImages={handleCombineImages}
							handleDelete={handleDelete}
							sendToFront={sendToFront}
							sendToBack={sendToBack}
							bringForward={bringForward}
							sendBackward={sendBackward}
							onZoomIn={zoomFunctions?.handleZoomIn}
							onZoomOut={zoomFunctions?.handleZoomOut}
							onZoomToFit={zoomFunctions?.handleZoomToFit}
						/>
					</ContextMenu>

					{/* Top Toolbar for desktop */}
					<TopToolbar
						selectedIds={selectedIds}
						images={images}
						handleDuplicate={handleDuplicate}
						handleRemoveBackground={handleRemoveBackground}
						handleUpscale={handleUpscale}
						handleCombineImages={handleCombineImages}
						handleDelete={handleDelete}
						setCroppingImageId={setCroppingImageId}
					/>

					<div className="absolute top-16 left-4 z-20 flex flex-col items-start gap-2">
						{/* Mobile tool icons - animated based on selection */}
						<MobileToolbar
							selectedIds={selectedIds}
							images={images}
							handleDuplicate={handleDuplicate}
							handleRemoveBackground={handleRemoveBackground}
							handleUpscale={handleUpscale}
							handleCombineImages={handleCombineImages}
							handleDelete={handleDelete}
							setCroppingImageId={setCroppingImageId}
							sendToFront={sendToFront}
							sendToBack={sendToBack}
							bringForward={bringForward}
							sendBackward={sendBackward}
						/>
					</div>

					<PromptBar
						selectedIds={selectedIds}
						images={images}
						onRun={handleRun}
						onUpload={triggerFileUpload}
						isGenerating={isGenerating || hasUploadingImages}
					/>

					{/* Zoom controls */}
					<ZoomControls
						viewport={viewport}
						setViewport={setViewport}
						canvasSize={canvasSize}
						images={images}
						onZoomFunctionsReady={setZoomFunctions}
					/>

					{/* Header dropdown */}
					<HeaderDropdown
						onZoomIn={zoomFunctions?.handleZoomIn}
						onZoomOut={zoomFunctions?.handleZoomOut}
						onZoomToFit={zoomFunctions?.handleZoomToFit}
						onUndo={undo}
						onRedo={redo}
						canUndo={canUndo}
						canRedo={canRedo}
					/>

					{/* Dimension display for selected images */}
					<DimensionDisplay selectedImages={images.filter((img) => selectedIds.includes(img.id))} viewport={viewport} />

					{/* User credits and avatar menu in top right corner */}
					<div className="absolute top-4 right-4 z-20 flex flex-row items-center gap-2">
						{session ? (
							<>
								{userData?.user?.membershipId === MembershipID.Free && (
									<Button
										size="sm"
										className="h-8 bg-blue-500 text-[13px] font-normal hover:bg-blue-500/80"
										onClick={() => setPlanBoxOpen(true)}
									>
										<GemIcon className="size-3.5" />
										Upgrade
									</Button>
								)}
								<UserMenu />
							</>
						) : (
							<Button size="sm" onClick={() => setSignInBoxOpen(true)} className="bg-foreground hover:bg-foreground/80 rounded-full">
								Sign In
							</Button>
						)}
					</div>
				</div>
			</main>
		</div>
	);
}
