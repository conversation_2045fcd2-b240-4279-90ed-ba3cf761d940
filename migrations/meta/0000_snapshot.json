{"version": "6", "dialect": "sqlite", "id": "fade8391-2411-4d67-870c-642cd84adcd2", "prevId": "********-0000-0000-0000-************", "tables": {"better_auth_account": {"name": "better_auth_account", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"better_auth_account_uid_unique": {"name": "better_auth_account_uid_unique", "columns": ["uid"], "isUnique": true}, "idx_better_auth_account_user_uid": {"name": "idx_better_auth_account_user_uid", "columns": ["user_uid"], "isUnique": false}, "uidx_better_auth_account_provider": {"name": "uidx_better_auth_account_provider", "columns": ["account_id", "provider_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "blog_category": {"name": "blog_category", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"blog_category_slug_unique": {"name": "blog_category_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "blog_post": {"name": "blog_post", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"blog_post_slug_unique": {"name": "blog_post_slug_unique", "columns": ["slug"], "isUnique": true}, "idx_blog_post_category": {"name": "idx_blog_post_category", "columns": ["category_id"], "isUnique": false}, "idx_blog_post_slug": {"name": "idx_blog_post_slug", "columns": ["slug"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "blog_post_translation": {"name": "blog_post_translation", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "post_id": {"name": "post_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "lang": {"name": "lang", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'en'"}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "meta_title": {"name": "meta_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "meta_description": {"name": "meta_description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "intro": {"name": "intro", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "html": {"name": "html", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "publish_date": {"name": "publish_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_blog_post_translation_post_id": {"name": "idx_blog_post_translation_post_id", "columns": ["post_id"], "isUnique": false}, "idx_blog_post_translation_lang": {"name": "idx_blog_post_translation_lang", "columns": ["lang"], "isUnique": false}, "uidx_blog_post_translation_post_id_lang": {"name": "uidx_blog_post_translation_post_id_lang", "columns": ["post_id", "lang"], "isUnique": true}, "idx_blog_post_translation_published_at": {"name": "idx_blog_post_translation_published_at", "columns": ["publish_date"], "isUnique": false}}, "foreignKeys": {"blog_post_translation_post_id_blog_post_id_fk": {"name": "blog_post_translation_post_id_blog_post_id_fk", "tableFrom": "blog_post_translation", "tableTo": "blog_post", "columnsFrom": ["post_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "changelog": {"name": "changelog", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "publish_date": {"name": "publish_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_changelog_published_at": {"name": "idx_changelog_published_at", "columns": ["publish_date"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "changelog_translation": {"name": "changelog_translation", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "changelog_id": {"name": "changelog_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "lang": {"name": "lang", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'en'"}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "html": {"name": "html", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "publish_date": {"name": "publish_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_changelog_translation_changelog_id": {"name": "idx_changelog_translation_changelog_id", "columns": ["changelog_id"], "isUnique": false}, "idx_changelog_translation_lang": {"name": "idx_changelog_translation_lang", "columns": ["lang"], "isUnique": false}, "uidx_changelog_translation_changelog_id_lang": {"name": "uidx_changelog_translation_changelog_id_lang", "columns": ["changelog_id", "lang"], "isUnique": true}, "idx_changelog_translation_status": {"name": "idx_changelog_translation_status", "columns": ["status"], "isUnique": false}, "idx_changelog_translation_published_at": {"name": "idx_changelog_translation_published_at", "columns": ["publish_date"], "isUnique": false}}, "foreignKeys": {"changelog_translation_changelog_id_changelog_id_fk": {"name": "changelog_translation_changelog_id_changelog_id_fk", "tableFrom": "changelog_translation", "tableTo": "changelog", "columnsFrom": ["changelog_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "media_task": {"name": "media_task", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'image'"}, "tool": {"name": "tool", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "media_head_uid": {"name": "media_head_uid", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "third_request_id": {"name": "third_request_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "visibility": {"name": "visibility", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "video_duration": {"name": "video_duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "aspect_ratio": {"name": "aspect_ratio", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "seed": {"name": "seed", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "request_body": {"name": "request_body", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits_source": {"name": "credits_source", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_media_task_user_uid": {"name": "idx_media_task_user_uid", "columns": ["user_uid"], "isUnique": false}, "idx_media_task_third_request_id": {"name": "idx_media_task_third_request_id", "columns": ["third_request_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "order": {"name": "order", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "order_id": {"name": "order_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "billing_reason": {"name": "billing_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "checkout_id": {"name": "checkout_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "subtotal_amount": {"name": "subtotal_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "discount_amount": {"name": "discount_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "tax_amount": {"name": "tax_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "net_amount": {"name": "net_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_amount": {"name": "total_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refunded_amount": {"name": "refunded_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refunded_tax_amount": {"name": "refunded_tax_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"order_order_id_unique": {"name": "order_order_id_unique", "columns": ["order_id"], "isUnique": true}, "idx_order_user_uid": {"name": "idx_order_user_uid", "columns": ["user_uid"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "better_auth_session": {"name": "better_auth_session", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"better_auth_session_uid_unique": {"name": "better_auth_session_uid_unique", "columns": ["uid"], "isUnique": true}, "idx_better_auth_session_token": {"name": "idx_better_auth_session_token", "columns": ["token"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "subscription": {"name": "subscription", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "recurring_interval": {"name": "recurring_interval", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "checkout_id": {"name": "checkout_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "current_period_start_at": {"name": "current_period_start_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "current_period_end_at": {"name": "current_period_end_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "canceled_at": {"name": "canceled_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_at": {"name": "start_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "end_at": {"name": "end_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "ended_at": {"name": "ended_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_cancellation_reason": {"name": "customer_cancellation_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"subscription_subscription_id_unique": {"name": "subscription_subscription_id_unique", "columns": ["subscription_id"], "isUnique": true}, "idx_subscription_user_uid": {"name": "idx_subscription_user_uid", "columns": ["user_uid"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_credits_history": {"name": "user_credits_history", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_uid": {"name": "user_uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits_free": {"name": "credits_free", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits_one_time": {"name": "credits_one_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credits_subscription": {"name": "credits_subscription", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"idx_user_credits_history_user_uid": {"name": "idx_user_credits_history_user_uid", "columns": ["user_uid"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "membership_id": {"name": "membership_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "membership_formatted": {"name": "membership_formatted", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'Free'"}, "credit_free": {"name": "credit_free", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "credit_free_ends_at": {"name": "credit_free_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credit_onetime": {"name": "credit_onetime", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "credit_onetime_ends_at": {"name": "credit_onetime_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "credit_sub": {"name": "credit_sub", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "credit_sub_ends_at": {"name": "credit_sub_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_id": {"name": "sub_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_period": {"name": "sub_period", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'none'"}, "sub_invoice_ends_at": {"name": "sub_invoice_ends_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "sub_expire_at": {"name": "sub_expire_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "ban": {"name": "ban", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_uid_unique": {"name": "user_uid_unique", "columns": ["uid"], "isUnique": true}, "user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "better_auth_verification": {"name": "better_auth_verification", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"better_auth_verification_uid_unique": {"name": "better_auth_verification_uid_unique", "columns": ["uid"], "isUnique": true}, "idx_better_auth_verification_identifier": {"name": "idx_better_auth_verification_identifier", "columns": ["identifier"], "isUnique": false}, "idx_better_auth_verification_expires_at": {"name": "idx_better_auth_verification_expires_at", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}