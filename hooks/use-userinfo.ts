import { useQuery, useQueryClient } from "@tanstack/react-query";
import { client } from "@/orpc/client";
import { UserInfoDB } from "@/@types/user";
import { userHasPaid } from "@/lib/utils-user";
import { MembershipID } from "@/@types/membership-type";

const USERINFO_QUERY_KEY = ["userinfo"];

const fetchUserWithOrpc = async (): Promise<UserInfoDB | null> => {
	try {
		const { userInfo } = await client.user.getUser();

		// orpc 客户端通常会在请求失败时抛出错误，
		// React Query 会自动捕获这个错误并设置 isError 状态
		return userInfo || null;
	} catch (error) {
		// 你可以在这里添加一些额外的日志记录
		console.error("Failed to fetch user via orpc:", error);
		// 重新抛出错误，以便 React Query 能够正确处理它
		throw error;
	}
};

// 这是我们的自定义 Hook
export const useUser = () => {
	return useQuery({
		// 1. Query Key: 唯一标识这个查询
		// 整个应用中，所有使用 USERINFO_QUERY_KEY 的查询都会共享同一个缓存
		queryKey: USERINFO_QUERY_KEY,

		// 2. Query Function: 获取数据的异步函数
		queryFn: fetchUserWithOrpc,

		// 3. Select: 转换/派生数据的强大选项
		//    这里的 `user` 是 `fetchUser` 返回的原始数据
		//    `select` 的返回值将成为 `useQuery` hook 最终返回的 `data`
		select: (user: UserInfoDB | null) => {
			if (!user) {
				return {
					user: null,
					hasPaid: false,
					creditsAll: 0,
					membershipLevel: "Free",
				};
			}

			const hasPaid = userHasPaid(user.membershipId, user.creditOneTimeEndsAt);
			const membershipLevel = hasPaid && user.membershipId === MembershipID.Free ? "Onetime" : user.membershipFormatted;

			const creditsAll = user.creditFree + user.creditOneTime + user.creditSubscription;

			return {
				user,
				hasPaid,
				creditsAll,
				membershipLevel,
			};
		},

		staleTime: Infinity, // 除非手动 invalidate，否则数据永远是新鲜的
		// staleTime: 30 * 60 * 1000, // 30分钟内数据被认为是新鲜的，不会触发后台刷新
		// refetchOnWindowFocus: true, // 当用户切换回浏览器标签页时，自动刷新用户信息
		// refetchOnReconnect: true, // 网络重连刷新
	});
};

// 创建一个用于手动刷新的 Hook，让逻辑更清晰
export const useRefreshUser = () => {
	const queryClient = useQueryClient();

	// 返回一个函数，调用它就会让 'user' 查询失效并重新获取
	return () => queryClient.invalidateQueries({ queryKey: USERINFO_QUERY_KEY });
};

export const useSetUser = () => {
	const queryClient = useQueryClient();
	return (user: UserInfoDB | null) => {
		queryClient.setQueryData(USERINFO_QUERY_KEY, user);
	};
};
